defmodule Repobot.PullRequest do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "pull_requests" do
    field :repository, :string
    field :branch_name, :string
    field :pull_request_number, :integer
    field :pull_request_url, :string
    field :status, :string, default: "open"

    many_to_many :source_files, Repobot.SourceFile,
      join_through: Repobot.PullRequestSourceFile,
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(pull_request, attrs) do
    pull_request
    |> cast(attrs, [
      :repository,
      :branch_name,
      :pull_request_number,
      :pull_request_url,
      :status
    ])
    |> validate_required([
      :repository,
      :branch_name,
      :pull_request_number,
      :pull_request_url
    ])
    |> validate_inclusion(:status, ["open", "closed", "merged"])
    |> unique_constraint([:repository, :pull_request_number],
      name: :pull_requests_repository_pull_request_number_index
    )
  end
end
