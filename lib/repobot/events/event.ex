defmodule Repobot.Events.Event do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "events" do
    field :type, :string
    field :payload, :map

    belongs_to :organization, Repobot.Accounts.Organization
    belongs_to :user, Repobot.Accounts.User
    belongs_to :repository, Repobot.Repository

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(event, attrs \\ %{}) do
    event
    |> cast(attrs, [:type, :payload, :organization_id, :user_id, :repository_id])
    |> validate_required([:type, :payload, :organization_id])
  end
end
