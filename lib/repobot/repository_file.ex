defmodule Repobot.RepositoryFile do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "repository_files" do
    field :path, :string
    field :name, :string
    field :type, :string
    field :size, :integer
    field :sha, :string
    field :content_updated_at, :utc_datetime

    # Virtual field that delegates to file_content.blob
    field :content, :string, virtual: true

    belongs_to :repository, Repobot.Repository
    has_one :file_content, Repobot.FileContent

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository_file, attrs \\ %{}) do
    repository_file
    |> cast(attrs, [
      :path,
      :name,
      :type,
      :size,
      :sha,
      :content,
      :content_updated_at,
      :repository_id
    ])
    |> validate_required([:path, :name, :type, :repository_id])
    |> foreign_key_constraint(:repository_id)
    |> unique_constraint([:repository_id, :path],
      name: :repository_files_repository_id_path_index
    )
    |> maybe_handle_file_content()
  end

  defp maybe_handle_file_content(changeset) do
    content_change = get_change(changeset, :content)

    if content_change do
      # Content is being updated, we need to handle FileContent
      # Note: This is a simplified approach. In practice, you might want to
      # handle this at the context layer to properly manage shared vs exclusive content
      changeset
    else
      changeset
    end
  end
end
