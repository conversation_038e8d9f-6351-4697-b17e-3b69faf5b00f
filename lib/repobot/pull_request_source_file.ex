defmodule Repobot.PullRequestSourceFile do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "pull_request_source_files" do
    belongs_to :pull_request, Repobot.PullRequest, type: :binary_id
    belongs_to :source_file, Repobot.SourceFile, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(pull_request_source_file, attrs) do
    pull_request_source_file
    |> cast(attrs, [:pull_request_id, :source_file_id])
    |> validate_required([:pull_request_id, :source_file_id])
    |> unique_constraint([:pull_request_id, :source_file_id])
  end
end
