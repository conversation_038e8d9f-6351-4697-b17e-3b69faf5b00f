defmodule Repobot.Repository do
  use Ecto.Schema

  import Ecto.Changeset

  @derive {Inspect, only: [:id, :name, :owner, :full_name]}
  @derive {JSON.Encoder,
           only: [
             :id,
             :name,
             :owner,
             :full_name,
             :language,
             :fork,
             :template,
             :private,
             :sync_mode
           ]}

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @sync_modes [:direct, :pr]

  schema "repositories" do
    field :name, :string
    field :owner, :string
    field :full_name, :string
    field :language, :string
    field :fork, :boolean, default: false
    field :template, :boolean, default: false
    field :private, :boolean, default: false
    field :sync_mode, Ecto.Enum, values: @sync_modes, default: :pr
    field :data, :map
    field :settings, :map, default: %{}

    belongs_to :folder, Repobot.Folder
    belongs_to :organization, Repobot.Accounts.Organization
    has_many :files, Repobot.RepositoryFile
    has_many :imported_files, Repobot.SourceFile, foreign_key: :source_repository_id

    many_to_many :source_files, Repobot.SourceFile,
      join_through: Repobot.RepositorySourceFile,
      on_replace: :delete

    # Template repositories can belong to multiple folders
    many_to_many :template_folders, Repobot.Folder,
      join_through: Repobot.RepositoryFolder,
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository, attrs \\ %{}) do
    repository
    |> cast(attrs, [
      :owner,
      :name,
      :full_name,
      :data,
      :folder_id,
      :language,
      :fork,
      :template,
      :private,
      :sync_mode,
      :settings,
      :organization_id
    ])
    |> validate_required([:owner, :name, :full_name, :organization_id])
    |> validate_inclusion(:sync_mode, @sync_modes)
    |> validate_template_folder()
  end

  # Ensure template repositories don't have a primary folder
  defp validate_template_folder(changeset) do
    case {get_field(changeset, :template), get_field(changeset, :folder_id)} do
      {true, folder_id} when not is_nil(folder_id) ->
        # If becoming a template and has a folder_id, clear it
        put_change(changeset, :folder_id, nil)

      {true, nil} ->
        # Template with no folder_id is valid
        changeset

      {false, _} ->
        # Non-template repositories can have a folder_id
        changeset
    end
  end
end
