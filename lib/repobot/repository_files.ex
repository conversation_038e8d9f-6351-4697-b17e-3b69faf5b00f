defmodule Repobot.RepositoryFiles do
  @moduledoc """
  The RepositoryFiles context.
  """

  require Logger

  import Ecto.Query, warn: false

  alias Repobot.Repo
  alias Repobot.RepositoryFile

  @doc """
  Returns the list of repository files for a given repository.
  """
  def list_repository_files(repository) do
    from(rf in RepositoryFile,
      left_join: fc in assoc(rf, :file_content),
      where: rf.repository_id == ^repository.id,
      select: %{rf | content: fc.blob}
    )
    |> Repo.all()
  end

  @doc """
  Gets a single repository file.
  Raises `Ecto.NoResultsError` if the Repository file does not exist.
  """
  def get_repository_file!(id) do
    from(rf in RepositoryFile,
      left_join: fc in assoc(rf, :file_content),
      where: rf.id == ^id,
      select: %{rf | content: fc.blob}
    )
    |> Repo.one!()
  end

  @doc """
  Gets a single repository file by repository_id and path.
  Returns nil if the file does not exist.
  """
  def get_repository_file_by_path(repository_id, path) do
    from(rf in RepositoryFile,
      left_join: fc in assoc(rf, :file_content),
      where: rf.repository_id == ^repository_id and rf.path == ^path,
      select: %{rf | content: fc.blob}
    )
    |> Repo.one()
  end

  @doc """
  Creates a repository file.
  """
  def create_repository_file(attrs \\ %{}) do
    content = Map.get(attrs, :content)

    # Use a transaction to ensure both RepositoryFile and FileContent are created together
    Repo.transaction(fn ->
      # Create the RepositoryFile first
      case %RepositoryFile{}
           |> RepositoryFile.changeset(attrs)
           |> Repo.insert() do
        {:ok, repository_file} ->
          # Create FileContent if content is provided
          if content do
            case Repo.insert(%Repobot.FileContent{
                   blob: content,
                   repository_file_id: repository_file.id
                 }) do
              {:ok, _fc} -> :ok
              {:error, changeset} -> Repo.rollback({:file_content_error, changeset})
            end
          end

          # FileContent is now associated via has_one, no need to update repository file
          repository_file

        {:error, changeset} ->
          Repo.rollback({:repository_file_error, changeset})
      end
    end)
    |> case do
      {:ok, repository_file} -> {:ok, repository_file}
      {:error, {_type, changeset}} -> {:error, changeset}
    end
  end

  @doc """
  Updates a repository file.
  """
  def update_repository_file(%RepositoryFile{} = repository_file, attrs) do
    content = Map.get(attrs, :content)

    # Preload file_content if not already loaded
    repository_file =
      if Ecto.assoc_loaded?(repository_file.file_content) do
        repository_file
      else
        Repo.preload(repository_file, :file_content)
      end

    Repo.transaction(fn ->
      # Handle content update if provided
      updated_attrs =
        if content do
          case repository_file.file_content do
            nil ->
              # Create new FileContent if none exists
              case Repo.insert(%Repobot.FileContent{
                     blob: content,
                     repository_file_id: repository_file.id
                   }) do
                {:ok, _file_content} ->
                  # FileContent is now associated via has_one, no need to update repository file
                  attrs

                {:error, changeset} ->
                  Repo.rollback({:file_content_error, changeset})
              end

            file_content ->
              # Update existing FileContent
              case Repo.update(Ecto.Changeset.change(file_content, blob: content)) do
                {:ok, _} -> attrs
                {:error, changeset} -> Repo.rollback({:file_content_update_error, changeset})
              end
          end
        else
          attrs
        end

      # Update the repository file
      case repository_file
           |> RepositoryFile.changeset(updated_attrs)
           |> Repo.update() do
        {:ok, updated_file} ->
          # Log file update event
          log_repository_file_update(updated_file, attrs)
          updated_file

        {:error, changeset} ->
          Repo.rollback({:repository_file_update_error, changeset})
      end
    end)
    |> case do
      {:ok, updated_file} -> {:ok, updated_file}
      {:error, {_type, changeset}} -> {:error, changeset}
    end
  end

  @doc """
  Deletes a repository file.
  """
  def delete_repository_file(%RepositoryFile{} = repository_file) do
    Repo.delete(repository_file)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking repository file changes.
  """
  def change_repository_file(%RepositoryFile{} = repository_file, attrs \\ %{}) do
    RepositoryFile.changeset(repository_file, attrs)
  end

  @doc """
  Syncs repository files with github_api().
  This will create, update, or delete repository files based on the GitHub tree.
  """
  def sync_repository_files(repository, user) do
    client = github_api().client(user)

    case github_api().get_tree(client, repository.owner, repository.name) do
      {:ok, tree} ->
        Logger.debug("Got tree from GitHub with #{length(tree)} items")

        # Get existing files for this repository
        existing_files =
          RepositoryFile
          |> where(repository_id: ^repository.id)
          |> Repo.all()
          |> Enum.group_by(& &1.path)

        Logger.debug("Found #{map_size(existing_files)} existing files in database")

        # Create a set of paths from the GitHub tree
        github_paths = MapSet.new(tree, & &1["path"])
        Logger.debug("GitHub paths: #{inspect(github_paths)}")

        # Find files that exist in our database but not in GitHub anymore
        files_to_delete =
          existing_files
          |> Map.keys()
          |> Enum.reject(&MapSet.member?(github_paths, &1))
          |> Enum.flat_map(&Map.get(existing_files, &1, []))

        Logger.debug("Files to delete: #{inspect(Enum.map(files_to_delete, & &1.path))}")

        # Start a transaction to ensure all operations succeed or fail together
        case Repo.transaction(fn ->
               # Delete files that no longer exist in GitHub
               Enum.each(files_to_delete, fn file ->
                 Logger.debug("Deleting file: #{file.path}")

                 case Repo.delete(file) do
                   {:ok, _} -> :ok
                   {:error, reason} -> Repo.rollback("Failed to delete file: #{inspect(reason)}")
                 end
               end)

               # Process each file in the tree
               results =
                 tree
                 |> Enum.map(fn item ->
                   Logger.debug("Processing tree item: #{inspect(item)}")
                   process_tree_item(item, repository, existing_files)
                 end)

               case handle_tree_processing_results(results) do
                 {:ok, files} ->
                   Logger.debug("Successfully processed #{length(files)} files")
                   {:ok, files}

                 {:error, reason} ->
                   Logger.error("Failed to process tree: #{reason}")
                   Repo.rollback(reason)
               end
             end) do
          {:ok, {:ok, files}} ->
            Logger.debug("Transaction completed successfully with #{length(files)} files")
            {:ok, files}

          {:ok, files} ->
            Logger.debug(
              "Transaction completed successfully (alt format) with #{length(files)} files"
            )

            {:ok, files}

          {:error, reason} ->
            Logger.error("Transaction failed: #{inspect(reason)}")
            {:error, reason}
        end

      {:error, reason} ->
        Logger.error("Failed to get tree from GitHub: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp process_tree_item(item, repository, existing_files) do
    case Map.get(existing_files, item["path"]) do
      [existing_file] ->
        # Update existing file if SHA changed
        if existing_file.sha != item["sha"] do
          Logger.debug("Updating existing file #{item["path"]} with new SHA")

          update_repository_file(existing_file, %{
            sha: item["sha"],
            size: item["size"],
            # Clear content since it needs to be refetched
            content_updated_at: nil
          })
        else
          Logger.debug("File #{item["path"]} unchanged")
          {:ok, existing_file}
        end

      nil ->
        Logger.debug("Creating new file #{item["path"]}")
        # Create new file
        create_repository_file(%{
          repository_id: repository.id,
          path: item["path"],
          name: Path.basename(item["path"]),
          type: item["type"],
          size: item["size"],
          sha: item["sha"]
        })

      _ ->
        Logger.error("Multiple files found with path #{item["path"]}")
        {:error, "Multiple files found with the same path"}
    end
  end

  defp handle_tree_processing_results(results) do
    {successes, failures} =
      results
      |> Enum.split_with(fn
        {:ok, _} -> true
        _ -> false
      end)

    case failures do
      [] -> {:ok, Enum.map(successes, fn {:ok, file} -> file end)}
      _ -> {:error, "Failed to process some files"}
    end
  end

  @doc """
  Fetches the content of a repository file from github_api().
  """
  def fetch_file_content(%RepositoryFile{} = repository_file, user) do
    repository = Repo.get!(Repobot.Repository, repository_file.repository_id)
    client = github_api().client(user)

    case github_api().get_file_content(
           client,
           repository.owner,
           repository.name,
           repository_file.path
         ) do
      {:ok, content, _response} ->
        update_repository_file(repository_file, %{
          content: content,
          content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
        })

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Gets repository files at a specific path.
  For directories, this returns the immediate children.
  For files, this returns the file itself.
  """
  def get_files_at_path(repository, path) do
    normalized_path = String.trim_leading(path, "/")

    # For root path, get files with no "/" in their path
    if path == "/" do
      from(rf in RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        where: rf.repository_id == ^repository.id,
        where: not like(rf.path, "%/%"),
        select: %{rf | content: fc.blob}
      )
      |> Repo.all()
    else
      # For other paths, get the exact file or immediate children
      parent_path = normalized_path <> "/"

      from(rf in RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        where: rf.repository_id == ^repository.id,
        where: rf.path == ^normalized_path or like(rf.path, ^(parent_path <> "%")),
        where: not like(rf.path, ^(parent_path <> "%/%")),
        select: %{rf | content: fc.blob}
      )
      |> Repo.all()
    end
  end

  def github_api() do
    Application.get_env(:repobot, :github_api)
  end

  defp log_repository_file_update(%RepositoryFile{} = repository_file, attrs) do
    # Only log if significant changes were made
    significant_changes = [:content, :sha, :size] |> Enum.any?(&Map.has_key?(attrs, &1))

    if significant_changes do
      payload = %{
        "file_id" => repository_file.id,
        "repository_id" => repository_file.repository_id,
        "path" => repository_file.path,
        "name" => repository_file.name,
        "size" => repository_file.size,
        "sha" => repository_file.sha,
        "changes" => Map.keys(attrs) |> Enum.map(&to_string/1)
      }

      # Get repository to access organization_id
      repository = Repobot.Repositories.get_repository!(repository_file.repository_id)

      Repobot.Events.log_file_update_event(
        "file",
        payload,
        repository.organization_id,
        nil,
        repository_file.repository_id
      )
    end
  end
end
