defmodule Repobot.Workers.EventHandlers.GitHub.RepositoryEdited do
  @moduledoc """
  Oban worker for handling GitHub repository edited events.

  This worker processes repository edit events that have been stored
  in the events table and updates the repository information in the database.
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.Repositories

  @impl true
  def handle(%Events.Event{} = event) do
    payload = event.payload
    repository_data = payload["repository"]

    github_id = repository_data["id"]
    full_name = repository_data["full_name"]

    Logger.info("Processing repository edit event",
      event_id: event.id,
      github_id: github_id,
      full_name: full_name
    )

    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping edit update",
          event_id: event.id,
          github_id: github_id,
          full_name: full_name
        )

        :ok

      repo ->
        Logger.info("Found repository for edit update",
          event_id: event.id,
          github_id: github_id,
          repository_id: repo.id,
          old_full_name: repo.full_name,
          new_full_name: full_name
        )

        case Repositories.update_repository_from_webhook(repo, repository_data) do
          {:ok, updated_repo} ->
            Logger.info("Repository successfully edited via event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: updated_repo.id,
              old_full_name: repo.full_name,
              new_full_name: updated_repo.full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to edit repository from event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              old_full_name: repo.full_name,
              new_full_name: full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end
end
