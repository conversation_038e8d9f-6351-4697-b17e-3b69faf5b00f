defmodule Repobot.Workers.EventHandlers.GitHub.RepositoryRenamed do
  @moduledoc """
  Oban worker for handling GitHub repository renamed events.

  This worker processes repository rename events that have been stored
  in the events table and updates the repository information in the database.
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.Repositories

  @impl true
  def handle(%Events.Event{} = event) do
    payload = event.payload
    repository_data = payload["repository"]
    changes = payload["changes"]

    github_id = repository_data["id"]
    new_full_name = repository_data["full_name"]
    old_name = get_in(changes, ["repository", "name", "from"])

    require Logger

    Logger.info("Processing repository rename event",
      event_id: event.id,
      github_id: github_id,
      old_name: old_name,
      new_full_name: new_full_name
    )

    case Repositories.get_repository_by_github_id(github_id) do
      nil ->
        Logger.info("Repository not found in database, skipping rename update",
          event_id: event.id,
          github_id: github_id,
          old_name: old_name,
          new_full_name: new_full_name
        )

        :ok

      repo ->
        Logger.info("Found repository for rename update",
          event_id: event.id,
          github_id: github_id,
          repository_id: repo.id,
          old_full_name: repo.full_name,
          new_full_name: new_full_name
        )

        case Repositories.update_repository_from_webhook(repo, repository_data) do
          {:ok, updated_repo} ->
            Logger.info("Repository successfully renamed via event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: updated_repo.id,
              old_full_name: repo.full_name,
              new_full_name: updated_repo.full_name
            )

            :ok

          {:error, reason} ->
            Logger.error("Failed to rename repository from event worker",
              event_id: event.id,
              github_id: github_id,
              repository_id: repo.id,
              old_full_name: repo.full_name,
              new_full_name: new_full_name,
              error: inspect(reason)
            )

            {:error, reason}
        end
    end
  end
end
