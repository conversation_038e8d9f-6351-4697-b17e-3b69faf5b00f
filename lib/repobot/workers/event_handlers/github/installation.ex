defmodule Repobot.Workers.EventHandlers.GitHub.Installation do
  @moduledoc """
  Oban worker for handling GitHub installation webhook events.

  This worker processes installation events that have been stored
  in the events table and updates organization installation_id in the database.
  """

  use Repobot.Workers.EventHandler, max_attempts: 3

  alias Repobot.{Accounts, Repo}
  alias Repobot.Accounts.Organization

  @impl true
  def handle(%Events.Event{} = event) do
    payload = event.payload
    action = payload["action"]
    installation = payload["installation"]
    account = installation["account"]

    installation_id = installation["id"]
    account_login = account["login"]

    Logger.info("Processing installation #{action} event",
      event_id: event.id,
      installation_id: installation_id,
      account_login: account_login,
      action: action
    )

    case find_organization(account) do
      nil ->
        Logger.info("No organization found for account, skipping installation #{action}",
          event_id: event.id,
          installation_id: installation_id,
          account_login: account_login,
          action: action
        )

        :ok

      org ->
        case action do
          "created" ->
            handle_installation_created(event, org, installation_id)

          "deleted" ->
            handle_installation_deleted(event, org, installation_id)

          _ ->
            Logger.info("Unhandled installation action, skipping",
              event_id: event.id,
              installation_id: installation_id,
              organization_id: org.id,
              action: action
            )

            :ok
        end
    end
  end

  defp handle_installation_created(event, org, installation_id) do
    Logger.info("Updating installation_id for organization",
      event_id: event.id,
      organization_id: org.id,
      organization_name: org.name,
      installation_id: installation_id
    )

    case Repo.update(Organization.changeset(org, %{installation_id: installation_id})) do
      {:ok, updated_org} ->
        Logger.info("Successfully updated installation_id for organization",
          event_id: event.id,
          organization_id: updated_org.id,
          organization_name: updated_org.name,
          installation_id: installation_id
        )

        :ok

      {:error, reason} ->
        Logger.error("Failed to update installation_id for organization",
          event_id: event.id,
          organization_id: org.id,
          organization_name: org.name,
          installation_id: installation_id,
          error: inspect(reason)
        )

        {:error, reason}
    end
  end

  defp handle_installation_deleted(event, org, installation_id) do
    Logger.info("Removing installation_id for organization",
      event_id: event.id,
      organization_id: org.id,
      organization_name: org.name,
      installation_id: installation_id
    )

    case Repo.update(Organization.changeset(org, %{installation_id: nil})) do
      {:ok, updated_org} ->
        Logger.info("Successfully removed installation_id for organization",
          event_id: event.id,
          organization_id: updated_org.id,
          organization_name: updated_org.name,
          installation_id: installation_id
        )

        :ok

      {:error, reason} ->
        Logger.error("Failed to remove installation_id for organization",
          event_id: event.id,
          organization_id: org.id,
          organization_name: org.name,
          installation_id: installation_id,
          error: inspect(reason)
        )

        {:error, reason}
    end
  end

  defp find_organization(%{"login" => login}) do
    case Accounts.get_organization_by_name(login) do
      nil ->
        Logger.info("Organization not found: #{login}")
        nil

      org ->
        org
    end
  end
end
