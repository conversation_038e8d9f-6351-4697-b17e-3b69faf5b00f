defmodule RepobotWeb.Live.Repositories.Index do
  @moduledoc false
  use RepobotWeb, :live_view

  alias Phoenix.LiveView.JS
  alias Repobot.{Repositories, Folders}
  alias Repobot.{Accounts, Folder}
  alias Repobot.Repo

  def mount(_params, session, socket) do
    current_user =
      Accounts.get_user!(session["current_user_id"])
      |> Repo.preload(:default_organization)

    # Ensure we have a valid organization
    if is_nil(socket.assigns[:current_organization]) do
      # If no current_organization, redirect to the root path
      {:ok,
       socket
       |> put_flash(:error, "No organization selected. Please sign in again.")
       |> push_navigate(to: "/")}
    else
      # Get initial repositories
      organization_id = socket.assigns.current_organization.id
      folders = Folders.list_folders(current_user, nil, organization_id)

      # Check if we need to refresh (no repositories found in *any* folder)
      needs_refresh =
        Enum.all?(folders, fn f ->
          Enum.empty?(f.repositories) && Enum.empty?(f.template_repositories)
        end) and
          Enum.empty?(
            Repositories.user_repositories(current_user, false, organization_id)
            |> Enum.filter(&is_nil(&1.folder_id))
          )

      socket =
        socket
        |> assign(:current_user, current_user)
        |> assign(:folders, folders)
        |> assign(:show_folder_form, false)
        |> assign(:search, "")
        |> assign(:refreshing, needs_refresh)
        |> assign(:folder_changeset, Folders.change_folder(%Folder{}))
        |> assign(:page_title, "Repositories")

      if connected?(socket) && needs_refresh do
        Process.send_after(self(), :do_refresh, 0)
      end

      {:ok, socket}
    end
  end

  def handle_event("search", %{"search" => search}, socket) do
    folders =
      Folders.list_folders(
        socket.assigns.current_user,
        search,
        socket.assigns.current_organization.id
      )

    {:noreply, socket |> assign(:search, search) |> assign(:folders, folders)}
  end

  def handle_event("refresh", _params, socket) do
    socket = assign(socket, :refreshing, true)
    Process.send_after(self(), :do_refresh, 0)
    {:noreply, socket}
  end

  def handle_event("show_folder_form", _params, socket) do
    {:noreply, assign(socket, :show_folder_form, true)}
  end

  def handle_event("hide_folder_form", _params, socket) do
    {:noreply, assign(socket, :show_folder_form, false)}
  end

  def handle_event("create_folder", %{"folder" => folder_params}, socket) do
    folder_params =
      folder_params
      |> Map.put("organization_id", socket.assigns.current_organization.id)

    case Folders.create_folder(folder_params) do
      {:ok, _folder} ->
        {:noreply,
         socket
         |> assign(
           :folders,
           Folders.list_folders(
             socket.assigns.current_user,
             nil,
             socket.assigns.current_organization.id
           )
         )
         |> assign(:show_folder_form, false)}

      {:error, changeset} ->
        {:noreply,
         socket
         |> assign(:folder_changeset, changeset)}
    end
  end

  def handle_event(
        "move_to_folder",
        %{"repository_id" => repository_id, "folder_id" => folder_id} = params,
        socket
      ) do
    repository = Repositories.get_repository!(repository_id)
    folder = Folders.get_folder!(folder_id)

    # Check if this is a template repository either from the database or from the drag-and-drop params
    # This ensures we handle template repositories correctly even when dragged from the template section
    is_template = repository.template || Map.get(params, "template_repo") == true

    # If the repository is not marked as a template but the template_repo flag is true,
    # we need to update the repository to be a template
    repository =
      if !repository.template && Map.get(params, "template_repo") == true do
        case Repositories.update_repository(repository, %{template: true}) do
          {:ok, updated_repository} -> updated_repository
          {:error, _} -> repository
        end
      else
        repository
      end

    # For template repositories, add to template_folders instead of changing folder_id
    if is_template do
      case Repositories.add_template_folder(repository, folder) do
        {:ok, _repository} ->
          folders =
            Folders.list_folders(
              socket.assigns.current_user,
              nil,
              socket.assigns.current_organization.id
            )

          {:noreply, socket |> assign(:folders, folders)}

        {:error, _changeset} ->
          {:noreply, socket}
      end
    else
      case Folders.add_repository_to_folder(repository, folder) do
        {:ok, _repository} ->
          folders =
            Folders.list_folders(
              socket.assigns.current_user,
              nil,
              socket.assigns.current_organization.id
            )

          {:noreply, socket |> assign(:folders, folders)}

        {:error, _changeset} ->
          {:noreply, socket}
      end
    end
  end

  def handle_event("remove_from_folder", %{"repository_id" => repository_id} = params, socket) do
    repository = Repositories.get_repository!(repository_id)

    case Map.get(params, "folder_id") do
      # If folder_id is present, it's a template repo removal from a specific folder
      folder_id when not is_nil(folder_id) ->
        folder = Folders.get_folder!(folder_id)

        case Repositories.remove_template_folder(repository, folder) do
          {:ok, _repository} ->
            folders =
              Folders.list_folders(
                socket.assigns.current_user,
                nil,
                socket.assigns.current_organization.id
              )

            {:noreply, socket |> assign(:folders, folders)}

          {:error, _changeset} ->
            {:noreply, socket}
        end

      # If folder_id is nil, it's a regular repo removal (unset primary folder_id)
      nil ->
        case Folders.remove_repository_from_folder(repository) do
          {:ok, _repository} ->
            folders =
              Folders.list_folders(
                socket.assigns.current_user,
                nil,
                socket.assigns.current_organization.id
              )

            {:noreply, socket |> assign(:folders, folders)}

          {:error, _changeset} ->
            {:noreply, socket}
        end
    end
  end

  def handle_event("toggle_star", %{"folder_id" => folder_id}, socket) do
    folder = Folders.get_folder!(folder_id)

    case Folders.toggle_starred(folder) do
      {:ok, _folder} ->
        {:noreply,
         socket
         |> assign(
           :folders,
           Folders.list_folders(
             socket.assigns.current_user,
             nil,
             socket.assigns.current_organization.id
           )
         )}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_event("set_as_template", %{"repository_id" => repository_id}, socket) do
    repository = Repositories.get_repository!(repository_id)

    # Store the current folder before updating to template
    current_folder =
      if repository.folder_id do
        Folders.get_folder!(repository.folder_id)
      else
        nil
      end

    case Repositories.update_repository(repository, %{template: true}) do
      {:ok, updated_repository} ->
        # After setting as template, add the repository to the template_folders association
        # of the current folder to maintain the association
        result =
          if current_folder do
            Repositories.add_template_folder(updated_repository, current_folder)
          else
            {:ok, updated_repository}
          end

        case result do
          {:ok, _repository} ->
            folders =
              Folders.list_folders(
                socket.assigns.current_user,
                nil,
                socket.assigns.current_organization.id
              )

            {:noreply,
             socket
             |> assign(:folders, folders)
             |> put_flash(
               :info,
               "Repository #{repository.full_name} is now a template repository."
             )}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(
               :error,
               "Repository was set as template but could not be added to the current folder: #{reason}"
             )}
        end

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to set repository as template.")}
    end
  end

  def handle_event(
        "disable_template",
        %{"repository_id" => repository_id, "folder_id" => folder_id},
        socket
      ) do
    repository =
      Repositories.get_repository!(repository_id) |> Repobot.Repo.preload(:template_folders)

    folder = Folders.get_folder!(folder_id)

    # First, update the repository to set template to false
    case Repositories.update_repository(repository, %{template: false}) do
      {:ok, updated_repository} ->
        # After disabling template, set the folder_id to maintain the association
        case Folders.add_repository_to_folder(updated_repository, folder) do
          {:ok, _repository} ->
            folders =
              Folders.list_folders(
                socket.assigns.current_user,
                nil,
                socket.assigns.current_organization.id
              )

            {:noreply,
             socket
             |> assign(:folders, folders)
             |> put_flash(:info, "Template disabled for #{repository.full_name}.")}

          {:error, _changeset} ->
            {:noreply,
             socket
             |> put_flash(:error, "Template was disabled but could not be added to the folder.")}
        end

      {:error, _changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to disable template for repository.")}
    end
  end

  def handle_info(:do_refresh, socket) do
    organization_id = socket.assigns.current_organization.id
    Repositories.user_repositories(socket.assigns.current_user, :refresh, organization_id)

    folders = Folders.list_folders(socket.assigns.current_user, nil, organization_id)

    {:noreply, socket |> assign(:folders, folders) |> assign(:refreshing, false)}
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-slate-900">Repositories</h1>
        <div class="flex gap-4 relative z-0">
          <form
            id="search-form"
            phx-change="search"
            phx-submit="search"
            phx-debounce="300"
            class="w-96"
          >
            <input
              type="text"
              name="search"
              value={@search}
              placeholder="Search repositories..."
              class="input"
            />
          </form>
          <button phx-click="show_folder_form" class="btn btn-soft">
            <.icon name="hero-folder-plus" class="w-4 h-4" /> New Folder
          </button>
          <button
            phx-click="refresh"
            disabled={@refreshing}
            class={["btn btn-soft", if(@refreshing, do: "btn-disabled", else: "")]}
          >
            <.icon name="hero-arrow-path" class={"w-4 h-4 #{if @refreshing, do: "animate-spin"}"} />
            <%= if @refreshing do %>
              Refreshing...
            <% else %>
              Refresh
            <% end %>
          </button>
        </div>
      </div>

      <%= if @show_folder_form do %>
        <div class="mb-8 p-4 bg-white rounded-lg border border-slate-200">
          <.form :let={f} for={@folder_changeset} phx-submit="create_folder" id="folder-form">
            <div class="flex gap-4 items-end">
              <div class="flex-1">
                <.input field={f[:name]} type="text" label="Folder Name" />
              </div>
              <div class="flex gap-2">
                <.btn type="submit" variant="primary">Create Folder</.btn>
                <.btn phx-click="hide_folder_form" variant="secondary">Cancel</.btn>
              </div>
            </div>
          </.form>
        </div>
      <% end %>

      <div class="grid grid-cols-3 gap-6" phx-hook="RepositoryDragDrop" id="repository-drag-drop">
        <%= if @refreshing do %>
          <div class="col-span-3 flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span class="ml-3 text-sm text-slate-600">Loading repositories...</span>
          </div>
        <% else %>
          <%= for folder <- Enum.sort_by(@folders, fn folder ->
                {
                  # First sort key: starred status (negative to put starred first)
                  (if folder.starred, do: 0, else: 1),
                  # Second sort key: repository count (negative to sort by descending)
                  # Use combined count from both lists
                  -(length(folder.repositories) + length(folder.template_repositories)),
                  # Third sort key: folder name
                  folder.name
                }
              end) do %>
            <div
              class="bg-white rounded-lg border border-slate-200 overflow-hidden h-[400px] flex flex-col"
              id={"folder-#{folder.id}"}
            >
              <div class="bg-slate-50 px-6 py-3 border-b border-slate-200 flex-none rounded-t-lg">
                <div class="flex items-center justify-between">
                  <h2 class="text-lg font-medium text-slate-900 flex items-center gap-2">
                    <.icon name="hero-folder" class="w-5 h-5 text-slate-400" />
                    <.link
                      navigate={~p"/folders/#{folder.id}"}
                      class="ml-2 text-large link link-hover"
                    >
                      {folder.name}
                    </.link>
                  </h2>
                  <button
                    phx-click="toggle_star"
                    phx-value-folder_id={folder.id}
                    class={[
                      "btn btn-ghost btn-sm btn-square",
                      if(folder.starred, do: "text-yellow-400", else: "text-slate-400")
                    ]}
                  >
                    <.icon name="hero-star" class="w-5 h-5" />
                  </button>
                </div>
                <p class="mt-1 text-sm text-slate-500">
                  {length(folder.repositories) + length(folder.template_repositories)} repositories
                </p>
              </div>
              <div
                class="divide-y divide-slate-200 overflow-y-auto flex-1"
                data-folder-content
                data-folder-id={folder.id}
              >
                <%= if Enum.empty?(folder.repositories) and Enum.empty?(folder.template_repositories) do %>
                  <div class="px-6 py-4 text-sm text-slate-500 italic">
                    No repositories in this folder yet. Move repositories here using the dropdown in the Unorganized Repositories section below.
                  </div>
                <% else %>
                  <%= # Render template repositories first
                  for template_repo <- Enum.sort_by(folder.template_repositories, & &1.full_name) do %>
                    <div
                      data-repo={template_repo.full_name}
                      data-template-repo="true"
                      data-repository-id={template_repo.id}
                      class="group px-6 py-4 flex items-center justify-between bg-emerald-50 border-b border-emerald-100"
                    >
                      <div
                        class="flex items-center cursor-grab opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                        data-drag-handle
                      >
                        <.icon name="hero-bars-2" class="w-4 h-4 text-emerald-400 mr-2" />
                      </div>
                      <div class="flex-1 min-w-0 flex items-center gap-2">
                        <.link
                          navigate={~p"/repositories/#{template_repo.id}"}
                          class="text-emerald-600 hover:text-emerald-900 font-medium block truncate"
                        >
                          {template_repo.full_name}
                        </.link>
                        <.icon name="hero-arrow-path-rounded-square" class="w-4 h-4 text-emerald-500" />
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                          Template
                        </span>
                      </div>
                      <div class="relative opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                        <button
                          class="btn btn-ghost btn-sm btn-square text-emerald-400"
                          id={"repo-menu-#{template_repo.id}-folder-#{folder.id}"}
                          phx-click={
                            JS.toggle(to: "#repo-dropdown-#{template_repo.id}-folder-#{folder.id}")
                          }
                        >
                          <.icon name="hero-ellipsis-horizontal" class="w-5 h-5" />
                        </button>
                        <div
                          id={"repo-dropdown-#{template_repo.id}-folder-#{folder.id}"}
                          class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 hidden z-10"
                          phx-click-away={
                            JS.hide(to: "#repo-dropdown-#{template_repo.id}-folder-#{folder.id}")
                          }
                        >
                          <div class="px-4 py-2 text-xs text-emerald-500">Actions</div>
                          <button
                            phx-click="remove_from_folder"
                            phx-value-repository_id={template_repo.id}
                            phx-value-folder_id={folder.id}
                            class="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                          >
                            Remove from folder
                          </button>
                          <button
                            phx-click="disable_template"
                            phx-value-repository_id={template_repo.id}
                            phx-value-folder_id={folder.id}
                            class="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                          >
                            Disable template
                          </button>
                        </div>
                      </div>
                    </div>
                  <% end %>
                  <%= # Then render regular repositories
                  for repo <- Enum.sort_by(folder.repositories, &{&1.fork, &1.full_name}) do %>
                    <div
                      data-repo={repo.full_name}
                      data-template-repo="false"
                      data-repository-id={repo.id}
                      class="group px-6 py-4 flex items-center justify-between hover:bg-slate-50"
                    >
                      <div
                        class="flex items-center cursor-grab opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                        data-drag-handle
                      >
                        <.icon name="hero-bars-2" class="w-4 h-4 text-slate-400 mr-2" />
                      </div>
                      <div class="flex-1 min-w-0 flex items-center gap-2">
                        <.link
                          navigate={~p"/repositories/#{repo.id}"}
                          class="link link-hover font-medium block truncate"
                        >
                          {repo.full_name}
                        </.link>
                        <%= if repo.fork do %>
                          <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                        <% end %>
                      </div>
                      <div class="relative opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                        <button
                          class="btn btn-ghost btn-sm btn-square text-slate-400"
                          id={"repo-menu-#{repo.id}-folder-#{folder.id}"}
                          phx-click={JS.toggle(to: "#repo-dropdown-#{repo.id}-folder-#{folder.id}")}
                        >
                          <.icon name="hero-ellipsis-horizontal" class="w-5 h-5" />
                        </button>
                        <div
                          id={"repo-dropdown-#{repo.id}-folder-#{folder.id}"}
                          class="absolute right-0 mt-2 w-48 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 hidden z-10"
                          phx-click-away={
                            JS.hide(to: "#repo-dropdown-#{repo.id}-folder-#{folder.id}")
                          }
                        >
                          <div class="px-4 py-2 text-xs text-slate-500">Actions</div>
                          <button
                            phx-click="remove_from_folder"
                            phx-value-repository_id={repo.id}
                            class="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                          >
                            Remove from folder
                          </button>
                          <button
                            phx-click="set_as_template"
                            phx-value-repository_id={repo.id}
                            class="block w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                          >
                            Set as template
                          </button>
                        </div>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              </div>
            </div>
          <% end %>
        <% end %>
      </div>

      <%= if not @refreshing do %>
        {# New section for unorganized template repositories}
        <% unorganized_templates =
          Repositories.user_repositories(@current_user, false, @current_organization.id)
          |> Enum.filter(&(&1.template && is_nil(&1.folder_id)))
          |> Enum.sort_by(& &1.full_name) %>

        <%= if Enum.any?(unorganized_templates) do %>
          <div class="mt-6 bg-white rounded-lg border border-emerald-200 overflow-hidden">
            <div class="bg-emerald-50 px-6 py-3 border-b border-emerald-200 rounded-t-lg">
              <h2 class="text-lg font-medium text-emerald-900 flex items-center gap-2">
                <.icon name="hero-arrow-path-rounded-square" class="w-5 h-5 text-emerald-600" />
                Template Repositories
                <span class="ml-2 text-sm text-emerald-700 font-normal">
                  (can be added to multiple folders)
                </span>
              </h2>
            </div>
            <div
              class="divide-y divide-emerald-100 max-h-[400px] overflow-y-auto"
              data-folder-content
              data-folder-id="unorganized"
              data-unorganized-section
            >
              <%= for repo <- unorganized_templates do %>
                <div
                  data-repo={repo.full_name}
                  data-template-repo="true"
                  data-repository-id={repo.id}
                  class="group px-6 py-4 flex items-center justify-between hover:bg-emerald-50"
                >
                  <div
                    class="flex items-center cursor-grab opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                    data-drag-handle
                  >
                    <.icon name="hero-bars-2" class="w-4 h-4 text-emerald-400 mr-2" />
                  </div>
                  <div class="flex-1 min-w-0 flex items-center gap-2">
                    <.link
                      navigate={~p"/repositories/#{repo.id}"}
                      class="text-emerald-600 hover:text-emerald-900 font-medium block truncate"
                    >
                      {repo.full_name}
                    </.link>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                      Template
                    </span>
                  </div>
                  <div class="flex items-center gap-2 flex-none ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                    <% # First preload the template_folders association to get current folders
                    repo_with_folders = repo |> Repo.preload(:template_folders)

                    # Get the IDs of folders where this template is already added
                    existing_folder_ids = Enum.map(repo_with_folders.template_folders, & &1.id)

                    # Filter out folders that already contain this template
                    available_folders =
                      Enum.reject(@folders, fn folder ->
                        Enum.member?(existing_folder_ids, folder.id)
                      end)
                      |> Enum.sort_by(& &1.name) %>

                    <%= if Enum.any?(available_folders) do %>
                      <form
                        id={"add-template-form-#{repo.id}"}
                        phx-submit="move_to_folder"
                        class="flex items-center gap-2"
                      >
                        <input type="hidden" name="repository_id" value={repo.id} />
                        <select
                          name="folder_id"
                          class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-emerald-900 ring-1 ring-inset ring-emerald-300 focus:ring-2 focus:ring-emerald-600 sm:text-sm sm:leading-6"
                          onchange="this.form.requestSubmit()"
                        >
                          <option value="">Add to folder...</option>
                          <%= for folder <- available_folders do %>
                            <option value={folder.id}>{folder.name}</option>
                          <% end %>
                        </select>
                      </form>
                    <% else %>
                      <div class="text-xs text-slate-500 italic">
                        Already in all available folders
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        {# Regular unorganized repositories section}
        <% unorganized_regular_repos =
          Repositories.user_repositories(@current_user, false, @current_organization.id)
          |> Enum.filter(&(is_nil(&1.folder_id) && !&1.template))
          |> Enum.sort_by(&{&1.fork, &1.full_name}) %>

        <%= if Enum.any?(unorganized_regular_repos) do %>
          <div class="mt-6 bg-white rounded-lg border border-slate-200 overflow-hidden">
            <div class="bg-slate-50 px-6 py-3 border-b border-slate-200 rounded-t-lg">
              <h2 class="text-lg font-medium text-slate-900">Unorganized Repositories</h2>
            </div>
            <div
              class="divide-y divide-slate-200 max-h-[400px] overflow-y-auto"
              data-folder-content
              data-folder-id="unorganized"
              data-unorganized-section
            >
              <%= for repo <- unorganized_regular_repos do %>
                <div
                  data-repo={repo.full_name}
                  data-template-repo="false"
                  data-repository-id={repo.id}
                  class="group px-6 py-4 flex items-center justify-between hover:bg-slate-50"
                >
                  <div
                    class="flex items-center cursor-grab opacity-0 group-hover:opacity-100 transition-opacity duration-150"
                    data-drag-handle
                  >
                    <.icon name="hero-bars-2" class="w-4 h-4 text-slate-400 mr-2" />
                  </div>
                  <div class="flex-1 min-w-0 flex items-center gap-2">
                    <.link
                      navigate={~p"/repositories/#{repo.id}"}
                      class="link link-hover font-medium block truncate"
                    >
                      {repo.full_name}
                    </.link>
                    <%= if repo.fork do %>
                      <.icon name="hero-arrow-path" class="w-4 h-4 text-slate-400" />
                    <% end %>
                  </div>
                  <div class="flex items-center gap-2 flex-none ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                    <form
                      id={"move-repo-form-#{repo.id}"}
                      phx-submit="move_to_folder"
                      class="flex items-center gap-2"
                    >
                      <input type="hidden" name="repository_id" value={repo.id} />
                      <select
                        name="folder_id"
                        class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-slate-900 ring-1 ring-inset ring-slate-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        onchange="this.form.requestSubmit()"
                      >
                        <option value="">Move to folder...</option>
                        <%= for folder <- @folders |> Enum.sort_by(& &1.name) do %>
                          <option value={folder.id}>{folder.name}</option>
                        <% end %>
                      </select>
                    </form>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end
end
