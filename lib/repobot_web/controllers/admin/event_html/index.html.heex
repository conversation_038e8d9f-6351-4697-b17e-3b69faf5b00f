<div class="p-6">
  <div class="mb-6 flex justify-between items-center">
    <h1 class="text-2xl font-semibold text-gray-900">Events</h1>
    <div class="text-sm text-gray-600">
      Showing {@total_events} events
    </div>
  </div>

  <div class="bg-white rounded-lg shadow mb-6 p-4">
    <h2 class="text-lg font-medium mb-4">Filters</h2>
    <form action={~p"/admin/events"} method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <fieldset class="fieldset">
        <label class="label">Event Type</label>
        <select name="type" class="select">
          <option value="">All Types</option>
          <%= for type <- @filter_options.types do %>
            <option value={type} selected={@filters.type == type}>{type}</option>
          <% end %>
        </select>
      </fieldset>

      <fieldset class="fieldset">
        <label class="label">Organization</label>
        <select name="organization_id" class="select">
          <option value="">All Organizations</option>
          <%= for org <- @filter_options.organizations do %>
            <option value={org.id} selected={@filters.organization_id == org.id}>
              {org.name}
            </option>
          <% end %>
        </select>
      </fieldset>

      <fieldset class="fieldset">
        <label class="label">Repository</label>
        <select name="repository_id" class="select">
          <option value="">All Repositories</option>
          <%= for repo <- @filter_options.repositories do %>
            <option value={repo.id} selected={@filters.repository_id == repo.id}>
              {repo.full_name}
            </option>
          <% end %>
        </select>
      </fieldset>

      <div class="md:col-span-3 flex justify-end gap-2">
        <.btn
          href={~p"/admin/events"}
          variant="outline"
          class="text-gray-700 bg-white border-gray-300 hover:bg-gray-50"
        >
          Clear Filters
        </.btn>
        <button
          type="submit"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700"
        >
          Apply Filters
        </button>
      </div>
    </form>
  </div>

  <div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table class="min-w-full table-auto">
      <thead class="bg-gray-50 border-b">
        <tr>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            ID
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Type
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Organization
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Repository
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Event Category
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Payload
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Timestamp
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <%= if Enum.empty?(@events) do %>
          <tr>
            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
              No events found
            </td>
          </tr>
        <% else %>
          <%= for event <- @events do %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {String.slice(event.id, 0, 8)}...
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {event.type}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {if event.organization, do: event.organization.name, else: "-"}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {if event.repository, do: event.repository.full_name, else: "-"}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <span class={"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{event_category_color(event.type)}"}>
                  {event_category(event.type)}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                {truncate_payload(event.payload)}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {format_datetime(event.inserted_at)}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <.btn
                  href={~p"/admin/events/#{event.id}"}
                  variant="soft"
                  size="xs"
                  class="text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
                >
                  View Details
                </.btn>
              </td>
            </tr>
          <% end %>
        <% end %>
      </tbody>
    </table>
  </div>
  
<!-- Pagination -->
  <div class="flex items-center justify-between mt-6">
    <div class="text-sm text-gray-700">
      Page {@page} of {@total_pages}
    </div>
    <div class="flex space-x-2">
      <%= if @page > 1 do %>
        <.btn
          href={~p"/admin/events?#{Map.put(@filters, "page", @page - 1)}"}
          variant="outline"
          class="text-gray-700 bg-white border-gray-300 hover:bg-gray-50"
        >
          Previous
        </.btn>
      <% end %>

      <%= if @page < @total_pages do %>
        <.btn
          href={~p"/admin/events?#{Map.put(@filters, "page", @page + 1)}"}
          variant="outline"
          class="text-gray-700 bg-white border-gray-300 hover:bg-gray-50"
        >
          Next
        </.btn>
      <% end %>
    </div>
  </div>
</div>
