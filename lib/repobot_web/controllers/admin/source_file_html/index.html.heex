<div class="p-6">
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Source Files</h1>
    <p class="mt-2 text-sm text-slate-600">Manage source files in your Repobot instance.</p>
  </div>

  <div class="overflow-hidden rounded-lg border border-slate-200">
    <table class="min-w-full divide-y divide-slate-200">
      <thead>
        <tr class="bg-slate-50">
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Name
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Target Path
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Organization
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Template
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Created
          </th>
          <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        <%= for file <- @source_files do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="text-sm font-medium text-slate-900">
                  {file.name}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                {file.target_path}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                {file.organization_id}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                <%= if file.is_template do %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Yes
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                    No
                  </span>
                <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
              {Calendar.strftime(file.inserted_at, "%Y-%m-%d")}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <.link navigate={~p"/admin/source-files/#{file}"} class="link link-hover">
                  View
                </.link>
                <.link navigate={~p"/admin/source-files/#{file}/edit"} class="link link-hover">
                  Edit
                </.link>
                <.link
                  href={~p"/admin/source-files/#{file}"}
                  method="delete"
                  data-confirm="Are you sure you want to delete this source file?"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </.link>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="mt-6 flex items-center justify-between">
    <div class="text-sm text-slate-500">
      Showing {length(@source_files)} of {@page.total_entries} source files
    </div>
    <div class="flex gap-2">
      <%= if @page.page_number > 1 do %>
        <.link
          navigate={~p"/admin/source-files?page=#{@page.page_number - 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Previous
        </.link>
      <% end %>

      <%= if @page.page_number < @page.total_pages do %>
        <.link
          navigate={~p"/admin/source-files?page=#{@page.page_number + 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Next
        </.link>
      <% end %>
    </div>
  </div>
</div>
