<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/source-files"} class="link link-hover">
          Source Files
        </.link>
      </li>
      <li>•</li>
      <li>
        <.link navigate={~p"/admin/source-files/#{@source_file}"} class="link link-hover">
          {@source_file.name}
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">Edit</li>
    </ol>
  </nav>

  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Edit Source File</h1>
    <p class="mt-2 text-sm text-slate-600">Update source file information and content.</p>
  </div>

  <div class="max-w-2xl">
    <.form :let={f} for={@changeset} action={~p"/admin/source-files/#{@source_file}"} method="put">
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 divide-y divide-slate-200">
        <div class="p-6">
          <h2 class="text-lg font-medium text-slate-900 mb-4">Basic Information</h2>
          <div class="space-y-4">
            <div>
              <.input field={f[:name]} type="text" label="Name" />
            </div>
            <div>
              <.input field={f[:target_path]} type="text" label="Target Path" />
            </div>
            <div>
              <label class="flex items-center gap-2">
                <.input field={f[:is_template]} type="checkbox" />
                <span class="text-sm font-medium text-slate-700">Is Template</span>
              </label>
            </div>
          </div>
        </div>

        <div class="p-6">
          <h2 class="text-lg font-medium text-slate-900 mb-4">Content</h2>
          <div>
            <.input field={f[:content]} type="textarea" label="Content" rows={20} />
          </div>
        </div>

        <div class="p-6 bg-slate-50 flex items-center justify-end gap-4">
          <.btn
            href={~p"/admin/source-files/#{@source_file}"}
            data-phx-link="redirect"
            data-phx-link-state="push"
            variant="secondary"
          >
            Cancel
          </.btn>
          <.btn type="submit" variant="primary">
            Save Changes
          </.btn>
        </div>
      </div>
    </.form>
  </div>
</div>
