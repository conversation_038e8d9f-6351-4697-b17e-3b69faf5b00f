defmodule RepobotWeb.Admin.EventHTML do
  use RepobotWeb, :html

  embed_templates "event_html/*"

  # Format timestamp for display
  def format_datetime(datetime) do
    case datetime do
      %DateTime{} ->
        datetime
        |> DateTime.truncate(:second)
        |> Calendar.strftime("%Y-%m-%d %H:%M:%S UTC")

      _ ->
        ""
    end
  end

  # Truncate the payload for display
  def truncate_payload(payload) when is_map(payload) do
    json = Jason.encode!(payload, pretty: false)

    if String.length(json) > 100 do
      String.slice(json, 0, 100) <> "..."
    else
      json
    end
  end

  def truncate_payload(_), do: ""

  # Determine event category based on type
  def event_category(type) do
    cond do
      String.starts_with?(type, "github.") -> "GitHub"
      String.starts_with?(type, "repobot.sync") -> "Sync"
      String.starts_with?(type, "repobot.worker") -> "Worker"
      String.starts_with?(type, "repobot.repository") -> "Repository"
      String.starts_with?(type, "repobot.") -> "Repobot"
      true -> "Other"
    end
  end

  # Format event category with badge color
  def event_category_color(type) do
    case event_category(type) do
      "GitHub" -> "bg-blue-100 text-blue-800"
      "Sync" -> "bg-green-100 text-green-800"
      "Worker" -> "bg-purple-100 text-purple-800"
      "Repository" -> "bg-orange-100 text-orange-800"
      "Repobot" -> "bg-indigo-100 text-indigo-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end
end
