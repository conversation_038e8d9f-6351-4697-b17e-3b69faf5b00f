defmodule RepobotWeb.WebhookController.RepositoryEditedTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.{Events, Repositories}
  alias Repobot.Workers.EventHandlers.GitHub.RepositoryEdited

  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    organization = organization_fixture()
    user = user_fixture(%{default_organization_id: organization.id})

    # Create a repository with GitHub data
    github_id = 123_456_789

    repository_data = %{
      "id" => github_id,
      "name" => "test-repo",
      "full_name" => "testorg/test-repo",
      "owner" => %{"login" => "testorg"},
      "private" => false,
      "description" => "Original description",
      "language" => "Elixir",
      "fork" => false
    }

    repository =
      create_repository(%{
        organization_id: organization.id,
        name: "test-repo",
        full_name: "testorg/test-repo",
        owner: "testorg",
        data: repository_data
      })

    %{user: user, organization: organization, repository: repository}
  end

  describe "repository edited webhook" do
    test "creates event and schedules worker for repository edit", %{
      conn: conn,
      repository: repository
    } do
      payload = %{
        "action" => "edited",
        "repository" => %{
          "id" => repository.data["id"],
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Updated description",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == repository.organization_id
      assert original_event.repository_id == repository.id
      assert original_event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated
      updated_repository = Repositories.get_repository!(repository.id)
      assert get_in(updated_repository.data, ["description"]) == "Updated description"

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1
    end

    test "handles repository not found in database", %{conn: conn} do
      payload = %{
        "action" => "edited",
        "repository" => %{
          # Non-existent repository
          "id" => 999_999_999,
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Updated description",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify no event was created since repository wasn't found
      events = Events.list_events()
      assert length(events) == 0
    end

    test "worker handles missing event", %{repository: _repository} do
      # Create a job with non-existent event_id (using a valid binary_id format)
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryEdited",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => non_existent_id}
      }

      result = RepositoryEdited.perform(job)
      assert {:error, error_message} = result
      assert error_message == "Event not found: #{non_existent_id}"
    end

    test "worker handles invalid job arguments", %{repository: _repository} do
      # Create a job without event_id
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryEdited",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"invalid" => "args"}
      }

      result = RepositoryEdited.perform(job)
      assert {:error, "Missing event_id in job arguments"} = result
    end

    test "successfully updates repository when name is changed", %{
      conn: conn,
      organization: organization
    } do
      # Create a repository with GitHub data
      github_id = 123_456_790

      original_repository_data = %{
        "id" => github_id,
        "name" => "old-repo-name",
        "full_name" => "#{organization.name}/old-repo-name",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          organization_id: organization.id,
          name: "old-repo-name",
          full_name: "#{organization.name}/old-repo-name",
          owner: organization.name,
          data: original_repository_data
        })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "new-repo-name",
        "full_name" => "#{organization.name}/new-repo-name",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == repo.organization_id
      assert original_event.repository_id == repo.id
      assert original_event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated
      updated_repository = Repositories.get_repository!(repo.id)
      assert updated_repository.name == "new-repo-name"
      assert updated_repository.full_name == "#{organization.name}/new-repo-name"
      assert updated_repository.data["name"] == "new-repo-name"
      assert updated_repository.data["full_name"] == "#{organization.name}/new-repo-name"

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1
    end

    test "successfully updates repository when moved to different owner", %{
      conn: conn,
      organization: organization
    } do
      # Create a repository with GitHub data
      github_id = 123_456_791

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{organization.name}/test-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          organization_id: organization.id,
          name: "test-repo",
          full_name: "#{organization.name}/test-repo",
          owner: organization.name,
          data: original_repository_data
        })

      # Create updated repository data with new owner
      new_owner = "new-owner"

      updated_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{new_owner}/test-repo",
        "owner" => %{"login" => new_owner},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == repo.organization_id
      assert original_event.repository_id == repo.id
      assert original_event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated
      updated_repository = Repositories.get_repository!(repo.id)
      assert updated_repository.name == "test-repo"
      assert updated_repository.full_name == "#{new_owner}/test-repo"
      assert updated_repository.owner == new_owner
      assert updated_repository.data["owner"]["login"] == new_owner
      assert updated_repository.data["full_name"] == "#{new_owner}/test-repo"

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1
    end

    test "successfully updates repository metadata (description, visibility, etc.)", %{
      conn: conn,
      organization: organization
    } do
      # Create a repository with GitHub data
      github_id = 123_456_792

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{organization.name}/test-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          organization_id: organization.id,
          name: "test-repo",
          full_name: "#{organization.name}/test-repo",
          owner: organization.name,
          private: false,
          data: original_repository_data
        })

      # Create updated repository data with changed metadata
      updated_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{organization.name}/test-repo",
        "owner" => %{"login" => organization.name},
        "private" => true,
        "description" => "Updated description with new information",
        "language" => "JavaScript",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == repo.organization_id
      assert original_event.repository_id == repo.id
      assert original_event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated
      updated_repository = Repositories.get_repository!(repo.id)
      assert updated_repository.name == "test-repo"
      assert updated_repository.full_name == "#{organization.name}/test-repo"
      assert updated_repository.owner == organization.name
      assert updated_repository.private == true
      assert updated_repository.language == "JavaScript"
      assert updated_repository.data["description"] == "Updated description with new information"
      assert updated_repository.data["private"] == true
      assert updated_repository.data["language"] == "JavaScript"

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1
    end

    test "handles database update error gracefully", %{conn: conn, organization: organization} do
      # Create a repository with GitHub data
      github_id = 123_456_793

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{organization.name}/test-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          organization_id: organization.id,
          name: "test-repo",
          full_name: "#{organization.name}/test-repo",
          owner: organization.name,
          data: original_repository_data
        })

      # Create updated repository data with invalid name (nil)
      updated_repository_data = %{
        "id" => github_id,
        "name" => nil,
        "full_name" => "#{organization.name}/test-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == repo.organization_id
      assert original_event.repository_id == repo.id
      assert original_event.payload == payload

      # Verify worker failure event was logged due to database error
      failure_events = Enum.filter(events, fn e -> e.type == "repobot.worker.failure" end)
      assert length(failure_events) >= 1

      # Verify original repository data is unchanged
      unchanged_repo = Repositories.get_repository!(repo.id)
      assert unchanged_repo.name == "test-repo"
      assert unchanged_repo.full_name == "#{organization.name}/test-repo"
    end

    test "updates template repository while preserving template status", %{
      conn: conn,
      organization: organization
    } do
      # Create a template repository with GitHub data
      github_id = 123_456_794

      original_repository_data = %{
        "id" => github_id,
        "name" => "template-repo",
        "full_name" => "#{organization.name}/template-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Template repository",
        "language" => "Elixir",
        "fork" => false
      }

      template_repo =
        create_repository(%{
          organization_id: organization.id,
          name: "template-repo",
          full_name: "#{organization.name}/template-repo",
          owner: organization.name,
          template: true,
          data: original_repository_data
        })

      # Create folders and associate with template repository
      folder = create_folder(%{name: "Frontend", organization_id: organization.id})

      Repobot.Repo.insert!(%Repobot.RepositoryFolder{
        repository_id: template_repo.id,
        folder_id: folder.id
      })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "renamed-template-repo",
        "full_name" => "#{organization.name}/renamed-template-repo",
        "owner" => %{"login" => organization.name},
        "private" => false,
        "description" => "Renamed template repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      # Make the webhook request
      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify event was created
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.repository.edited" end)
      assert original_event
      assert original_event.organization_id == template_repo.organization_id
      assert original_event.repository_id == template_repo.id
      assert original_event.payload == payload

      # Verify worker was scheduled and executed (in inline mode)
      # The repository should be updated but template status preserved
      updated_repository = Repositories.get_repository!(template_repo.id)
      assert updated_repository.name == "renamed-template-repo"
      assert updated_repository.full_name == "#{organization.name}/renamed-template-repo"
      assert updated_repository.template == true
      assert updated_repository.data["name"] == "renamed-template-repo"
      assert updated_repository.data["description"] == "Renamed template repository"

      # Verify folder associations are preserved
      folder_assoc =
        Repobot.Repo.get_by(Repobot.RepositoryFolder, repository_id: template_repo.id)

      assert folder_assoc != nil
      assert folder_assoc.folder_id == folder.id

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1
    end
  end
end
