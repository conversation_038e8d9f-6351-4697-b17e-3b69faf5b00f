defmodule RepobotWeb.WebhookController.InstallationTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  import Mox
  import Ecto.Query

  alias Repobot.{Events, Repo}
  alias Repobot.Accounts.Organization
  alias Repobot.Workers.EventHandlers.GitHub.Installation, as: InstallationWorker

  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    :ok
  end

  describe "installation webhook events" do
    setup do
      # Create a test organization
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "creates event and schedules worker when installation is created", %{
      conn: conn,
      org: org
    } do
      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify event was created and processed
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.installation.created" end)
      assert original_event
      assert original_event.organization_id == org.id
      assert original_event.payload == payload

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1

      # Verify organization was updated
      updated_org = Repo.get(Organization, org.id)
      assert updated_org.installation_id == 123_456
    end

    test "creates event and schedules worker when installation is deleted", %{
      conn: conn,
      org: org
    } do
      # First set an installation_id
      {:ok, org} = Repo.update(Organization.changeset(org, %{installation_id: 123_456}))
      assert org.installation_id == 123_456

      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "deleted",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify event was created and processed
      events = Events.list_events()
      assert length(events) >= 1

      # Find the original event
      original_event = Enum.find(events, fn e -> e.type == "github.installation.deleted" end)
      assert original_event
      assert original_event.organization_id == org.id
      assert original_event.payload == payload

      # Verify worker success event was logged
      success_events = Enum.filter(events, fn e -> e.type == "repobot.worker.success" end)
      assert length(success_events) >= 1

      # Verify installation_id was removed
      updated_org = Repo.get(Organization, org.id)
      assert is_nil(updated_org.installation_id)
    end

    test "ignores installation events for unknown organizations", %{conn: conn} do
      # Simulate GitHub installation webhook payload for unknown org
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => "unknown-org",
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no event was created
      events = Events.list_events()
      assert length(events) == 0
    end
  end

  describe "installation worker" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "worker processes installation created event successfully", %{org: org} do
      # Create an event for installation creation
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.installation.created",
          payload,
          org.id,
          nil,
          nil
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.Installation",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = InstallationWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the organization was updated
      updated_org = Repo.get!(Organization, org.id)
      assert updated_org.installation_id == 123_456

      # Assert a worker success event was logged
      success_events = Repo.all(from e in Events.Event, where: e.type == "repobot.worker.success")
      assert length(success_events) >= 1
    end

    test "worker processes installation deleted event successfully", %{org: org} do
      # First set an installation_id
      {:ok, org} = Repo.update(Organization.changeset(org, %{installation_id: 123_456}))
      assert org.installation_id == 123_456

      # Create an event for installation deletion
      payload = %{
        "action" => "deleted",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.installation.deleted",
          payload,
          org.id,
          nil,
          nil
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 2,
        worker: "Repobot.Workers.EventHandlers.GitHub.Installation",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = InstallationWorker.perform(job)

      # Assert the job succeeded
      assert result == :ok

      # Assert the installation_id was removed
      updated_org = Repo.get!(Organization, org.id)
      assert is_nil(updated_org.installation_id)

      # Assert a worker success event was logged
      success_events = Repo.all(from e in Events.Event, where: e.type == "repobot.worker.success")
      assert length(success_events) >= 1
    end

    test "worker handles installation event for unknown organization", %{org: org} do
      # Create an event for installation on unknown organization
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => "unknown-org",
            "type" => "User"
          }
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.installation.created",
          payload,
          org.id,
          nil,
          nil
        )

      # Create and perform the job
      job = %Oban.Job{
        id: 3,
        worker: "Repobot.Workers.EventHandlers.GitHub.Installation",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      # Perform the job
      result = InstallationWorker.perform(job)

      # Assert the job succeeded (no error for unknown org)
      assert result == :ok

      # Assert a worker success event was logged
      success_events = Repo.all(from e in Events.Event, where: e.type == "repobot.worker.success")
      assert length(success_events) >= 1
    end

    test "worker handles missing event", %{org: _org} do
      # Create a job with non-existent event_id (using a valid binary_id format)
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 4,
        worker: "Repobot.Workers.EventHandlers.GitHub.Installation",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => non_existent_id}
      }

      result = InstallationWorker.perform(job)
      assert {:error, error_message} = result
      assert error_message == "Event not found: #{non_existent_id}"
    end

    test "worker handles invalid job arguments", %{org: _org} do
      # Create a job without event_id
      job = %Oban.Job{
        id: 5,
        worker: "Repobot.Workers.EventHandlers.GitHub.Installation",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"invalid" => "args"}
      }

      result = InstallationWorker.perform(job)
      assert {:error, "Missing event_id in job arguments"} = result
    end
  end
end
