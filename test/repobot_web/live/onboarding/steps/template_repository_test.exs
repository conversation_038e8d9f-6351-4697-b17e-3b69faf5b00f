defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepositoryTest do
  use RepobotWeb.ConnCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures
  import Mox

  alias RepobotWeb.Live.Onboarding.Steps.TemplateRepository

  setup :verify_on_exit!

  # Helper function to create a proper LiveView socket for testing
  defp create_test_socket(assigns) do
    %Phoenix.LiveView.Socket{
      assigns:
        Map.merge(
          %{
            __changed__: %{},
            flash: %{},
            repositories: [],
            loading_repositories: false,
            loading_progress: 0,
            loading_message: "",
            loading_error: nil,
            selection_mode: :create,
            template_repo: nil
          },
          assigns
        )
    }
  end

  describe "component update functions" do
    test "handles repository loading completion successfully" do
      user = user_fixture()
      organization = organization_fixture()
      repository = create_repository(%{name: "test-repo", organization_id: organization.id})

      socket =
        create_test_socket(%{
          current_user: user,
          current_organization: organization,
          loading_repositories: true
        })

      # Simulate repository loading completion
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_complete: {:ok, [repository]}},
          socket
        )

      assert updated_socket.assigns.repositories == [repository]
      assert updated_socket.assigns.loading_repositories == false
      assert updated_socket.assigns.loading_progress == 100
    end

    test "handles repository loading failure gracefully" do
      user = user_fixture()
      organization = organization_fixture()

      socket =
        create_test_socket(%{
          current_user: user,
          current_organization: organization,
          loading_repositories: true
        })

      # Simulate repository loading failure
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_complete: {:error, "API rate limit exceeded"}},
          socket
        )

      assert updated_socket.assigns.loading_repositories == false
      assert updated_socket.assigns.loading_error == "API rate limit exceeded"
    end

    test "updates progress during repository loading" do
      user = user_fixture()
      organization = organization_fixture()

      socket =
        create_test_socket(%{
          current_user: user,
          current_organization: organization,
          loading_repositories: true,
          loading_progress: 0,
          loading_message: ""
        })

      # Simulate progress update
      {:ok, updated_socket} =
        TemplateRepository.update(
          %{repository_loading_progress: {75, "Processing repositories..."}},
          socket
        )

      assert updated_socket.assigns.loading_progress == 75
      assert updated_socket.assigns.loading_message == "Processing repositories..."
    end

    test "falls back to synchronous loading when Oban job fails to enqueue" do
      user = user_fixture()
      organization = organization_fixture()

      # Create a repository to simulate cached data
      create_repository(%{
        name: "fallback-repo",
        organization_id: organization.id
      })

      socket =
        create_test_socket(%{
          current_user: user,
          current_organization: organization
        })

      # The step should fall back to synchronous loading if async fails
      # This is tested indirectly by ensuring repositories are loaded
      {:ok, updated_socket} = TemplateRepository.update(%{}, socket)

      # Should have repositories assigned (either from cache or fallback)
      assert Map.has_key?(updated_socket.assigns, :repositories)
    end
  end

  describe "integration tests" do
    test "shows create new repository option by default", %{conn: conn} do
      user = user_fixture()

      # Mock GitHub client calls that might be triggered during repository loading
      # Use stub to avoid verification errors if the calls don't happen
      stub(Repobot.Test.GitHubMock, :client, fn _user ->
        %Tentacat.Client{auth: %{access_token: "test_token"}}
      end)

      stub(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        {:ok, []}
      end)

      stub(Repobot.Test.GitHubMock, :list_repos, fn _client, _org ->
        {:ok, []}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - should show create option selected by default
      html = render(view)
      assert html =~ "Create New Repository"
      assert has_element?(view, "input[id='create_new'][checked]")
    end

    test "shows select existing option when repositories are available", %{conn: conn} do
      user = user_fixture()

      # Create a repository for the user
      create_repository(%{
        name: "test-repo",
        organization_id: user.default_organization_id
      })

      # Mock GitHub client calls (though they shouldn't be needed since we have cached repos)
      stub(Repobot.Test.GitHubMock, :client, fn _user ->
        %Tentacat.Client{auth: %{access_token: "test_token"}}
      end)

      stub(Repobot.Test.GitHubMock, :user_repos, fn _client, _username ->
        {:ok, []}
      end)

      stub(Repobot.Test.GitHubMock, :list_repos, fn _client, _org ->
        {:ok, []}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - should show both options available
      html = render(view)
      assert html =~ "Create New Repository"
      assert html =~ "Select Existing Repository"
      # The "Select Existing Repository" radio button should not be disabled
      refute has_element?(view, "input[id='select_existing'][disabled]")
    end
  end
end
