defmodule RepobotWeb.Live.Dashboard.IndexTest do
  use RepobotWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  use Repobot.Test.Fixtures
  alias Repobot.Events.Event
  alias Repobot.PullRequest
  alias Repobot.Repo

  setup do
    user = create_user()
    # Organization is already created with the user
    org = user.default_organization

    # Create repositories with names
    repo1 =
      create_repository(%{
        organization_id: org.id,
        name: "repo1",
        owner: user.login,
        full_name: "#{user.login}/repo1"
      })

    repo2 =
      create_repository(%{
        organization_id: org.id,
        name: "repo2",
        owner: user.login,
        full_name: "#{user.login}/repo2"
      })

    # Create repository files for the repos
    create_repository_files(repo1)
    create_repository_files(repo2)

    # Create a source file for the PR
    source_file =
      create_source_file(%{
        name: "test-file.ex",
        content: "defmodule Test do\nend",
        user_id: user.id,
        organization_id: org.id,
        target_path: "lib/test.ex"
      })

    # Create some events with proper event types
    %Event{}
    |> Event.changeset(%{
      type: "repobot.sync",
      payload: %{"commit_sha" => "abc1234", "file_ids" => [1, 2, 3]},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    %Event{}
    |> Event.changeset(%{
      type: "github.push",
      payload: %{"commits" => [%{"message" => "Test commit"}], "ref" => "refs/heads/main"},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    %Event{}
    |> Event.changeset(%{
      type: "github.pull_request",
      payload: %{"action" => "opened", "number" => 1},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    # Create a PR
    %PullRequest{}
    |> PullRequest.changeset(%{
      repository: "#{user.login}/repo1",
      branch_name: "feature/test",
      pull_request_number: 1,
      pull_request_url: "https://github.com/#{user.login}/repo1/pull/1",
      status: "open",
      source_file_id: source_file.id
    })
    |> Repobot.Repo.insert!()

    %{user: user, organization: org, repos: [repo1, repo2]}
  end

  describe "Dashboard" do
    test "renders dashboard with stats and activity", %{conn: conn, user: user, organization: org} do
      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert has_element?(view, "h1", "Dashboard")
      assert has_element?(view, "h3", "Sync Activity")
      assert has_element?(view, "h3", "Recent Events")
      assert has_element?(view, "h3", "Repository Insights")
    end

    test "displays repository events in chronological order with latest on top", %{
      conn: conn,
      user: user,
      organization: org,
      repos: [repo1, repo2]
    } do
      now = DateTime.utc_now()

      # Force clear any existing events to have a clean state
      Repo.delete_all(Event)

      # Create older event for repo2 (3rd)
      _older_event =
        %Event{}
        |> Event.changeset(%{
          type: "repobot.sync",
          payload: %{"commit_sha" => "abc1234", "file_ids" => [1, 2, 3]},
          status: "success",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo2.id,
          # 1 hour ago
          inserted_at: DateTime.add(now, -3600, :second)
        })
        |> Repo.insert!()

      # Create middle event for repo2 (2nd)
      _middle_event =
        %Event{}
        |> Event.changeset(%{
          type: "github.pull_request",
          payload: %{"action" => "opened", "number" => 5},
          status: "completed",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo2.id,
          # 30 minutes ago
          inserted_at: DateTime.add(now, -1800, :second)
        })
        |> Repo.insert!()

      # Create newest event for repo1 (1st/top)
      _newest_event =
        %Event{}
        |> Event.changeset(%{
          type: "github.push",
          payload: %{"commits" => [%{"message" => "Latest commit"}], "ref" => "refs/heads/main"},
          status: "completed",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo1.id,
          inserted_at: now
        })
        |> Repo.insert!()

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert has_element?(view, "h3", "Recent Events")
    end

    test "handles dashboard render when there are no repositories", %{
      conn: conn,
      user: user,
      organization: org
    } do
      Repo.delete_all(Repobot.Repository)

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert has_element?(view, "h1", "Dashboard")
    end

    test "displays correct event types in Recent Events section", %{
      conn: conn,
      user: user,
      organization: org,
      repos: [repo1, _repo2]
    } do
      # Clear existing events
      Repo.delete_all(Event)

      # Make repo1 a template repository
      repo1 = Repo.update!(Ecto.Changeset.change(repo1, template: true))

      # Create a push to template repository (should show)
      %Event{}
      |> Event.changeset(%{
        type: "github.push",
        payload: %{
          "commits" => [%{"message" => "Update template"}],
          "ref" => "refs/heads/main"
        },
        organization_id: org.id,
        user_id: user.id,
        repository_id: repo1.id
      })
      |> Repo.insert!()

      # Create a PR opened by sync (should show)
      %Event{}
      |> Event.changeset(%{
        type: "github.pull_request.opened",
        payload: %{
          "action" => "opened",
          "pull_request" => %{
            "title" => "Update config.yml",
            "body" =>
              "This PR was automatically created by Repobot to update `config.yml`.\n\nThe file content is managed centrally and synchronized across repositories.",
            "number" => 42,
            "user" => %{
              "login" => "repobot-app[bot]"
            }
          },
          "repository" => %{
            "full_name" => repo1.full_name
          }
        },
        organization_id: org.id,
        user_id: user.id,
        repository_id: repo1.id
      })
      |> Repo.insert!()

      # Create a sync success event with direct push mode (should show)
      %Event{}
      |> Event.changeset(%{
        type: "repobot.sync.success",
        payload: %{
          "template_repository_name" => "template/repo",
          "target_repository_name" => "target/repo",
          "files_count" => 2,
          "sync_mode" => "direct"
        },
        organization_id: org.id,
        repository_id: repo1.id
      })
      |> Repo.insert!()

      # Create a sync success event with PR mode (should NOT show)
      %Event{}
      |> Event.changeset(%{
        type: "repobot.sync.success",
        payload: %{
          "template_repository_name" => "template/repo",
          "target_repository_name" => "target/repo",
          "files_count" => 1,
          "sync_mode" => "pull_request"
        },
        organization_id: org.id,
        repository_id: repo1.id
      })
      |> Repo.insert!()

      # Create a regular PR event (not created by Repobot, should NOT show)
      %Event{}
      |> Event.changeset(%{
        type: "github.pull_request.opened",
        payload: %{
          "action" => "opened",
          "pull_request" => %{
            "title" => "Add new feature",
            "body" => "This is a regular PR created by a developer",
            "number" => 43
          },
          "repository" => %{
            "full_name" => repo1.full_name
          }
        },
        organization_id: org.id,
        user_id: user.id,
        repository_id: repo1.id
      })
      |> Repo.insert!()

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      # Should show Recent Events section
      assert has_element?(view, "h3", "Recent Events")

      # Verify that the correct events are being processed
      # We should see:
      # 1. Push to template repository
      # 2. PR opened by sync
      # 3. Sync success with direct push
      # We should NOT see:
      # 4. Sync success with PR mode
      # 5. Regular PR not created by Repobot

      # Check for push event
      assert has_element?(view, "[data-test-event-type='push']") ||
               has_element?(view, "span", "pushed to template repository")

      # Check for sync PR event
      assert has_element?(view, "[data-test-event-type='pr_opened']") ||
               has_element?(view, "span", "opened sync pull request")

      # Check for direct sync event
      assert has_element?(view, "[data-test-event-type='sync_direct']") ||
               has_element?(view, "span", "synced") ||
               has_element?(view, "span", "direct push")
    end

    test "displays sync activity chart with correct data", %{
      conn: conn,
      user: user,
      organization: org,
      repos: [repo1, _repo2]
    } do
      # Clear existing events
      Repo.delete_all(Event)

      # Make repo1 a template repository
      repo1 = Repo.update!(Ecto.Changeset.change(repo1, template: true))

      # Create some sync events for the chart
      now = DateTime.utc_now()

      # Create a sync success event
      %Event{}
      |> Event.changeset(%{
        type: "repobot.sync.success",
        payload: %{
          "template_repository_name" => "template/repo",
          "target_repository_name" => "target/repo",
          "files_count" => 2
        },
        organization_id: org.id,
        repository_id: repo1.id,
        inserted_at: now
      })
      |> Repo.insert!()

      # Create a push to template repository
      %Event{}
      |> Event.changeset(%{
        type: "github.push",
        payload: %{
          "commits" => [%{"message" => "Update template"}],
          "ref" => "refs/heads/main"
        },
        organization_id: org.id,
        user_id: user.id,
        repository_id: repo1.id,
        inserted_at: DateTime.add(now, -3600, :second)
      })
      |> Repo.insert!()

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      # Should show Sync Activity chart section
      assert has_element?(view, "h3", "Sync Activity")
      assert has_element?(view, "#sync-activity-chart")
      assert has_element?(view, "canvas")

      # Should show total events count
      assert has_element?(view, "div", "total events")
    end
  end
end
