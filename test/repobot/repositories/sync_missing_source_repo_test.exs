defmodule Repobot.Repositories.SyncMissingSourceRepoTest do
  use Repobot.DataCase

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.Repositories.Sync

  setup :verify_on_exit!

  describe "install_file/5 with missing source repository" do
    test "handles nil source_repository gracefully and defaults to direct mode" do
      user = create_user()

      # Create a source file without a source repository
      source_file =
        create_source_file(%{
          name: "test.ex",
          content: "test content",
          target_path: "lib/test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: nil
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: "target-owner",
          full_name: "target-owner/target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          settings: %{"provider" => "github"}
        })

      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Mock the GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:create_file, fn _client, _owner, _repo, _path, _content, _message, _sha ->
        {201, %{"content" => %{"sha" => "abc123"}}, %{}}
      end)

      # Should not raise an error and should work with nil source_repository
      assert {:ok, "File created successfully"} =
               Sync.install_file(
                 source_file,
                 # nil source_repository should be handled gracefully
                 nil,
                 target_repo,
                 test_client,
                 mode: :direct
               )
    end
  end

  describe "sync_file/5 with missing source repository" do
    test "handles nil source_repository gracefully and defaults to direct mode" do
      user = create_user()

      # Create a source file without a source repository
      source_file =
        create_source_file(%{
          name: "test.ex",
          content: "test content",
          target_path: "lib/test.ex",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: nil
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: "target-owner",
          full_name: "target-owner/target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          settings: %{"provider" => "github"}
        })

      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Mock the GitHub API calls for file not found (404) case
      Repobot.Test.GitHubMock
      |> expect(:get_file_content, fn _client, _owner, _repo, _path ->
        {:error, 404}
      end)
      |> expect(:create_file, fn _client, _owner, _repo, _path, _content, _message, _sha ->
        {201, %{"content" => %{"sha" => "abc123"}}, %{}}
      end)

      # Should not raise an error and should work with nil source_repository
      assert {:ok, "File created successfully"} =
               Sync.sync_file(
                 source_file,
                 # nil source_repository should be handled gracefully
                 nil,
                 target_repo,
                 test_client,
                 mode: :direct
               )
    end
  end
end
