defmodule Repobot.Repositories.SyncTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.Repositories.Sync
  alias Repobot.Repo

  setup :verify_on_exit!

  describe "sync_changes/5 with FileContent integration" do
    test "correctly handles template rendering with FileContent schema" do
      user = create_user()

      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: "template-owner",
          full_name: "template-owner/template-repo",
          sync_mode: :direct,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: "target-owner",
          full_name: "target-owner/target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          data: %{"name" => "target-repo", "description" => "A test repository"},
          settings: %{"project" => "test-project"}
        })

      # Create a template source file with template variables
      template_content =
        "# {{ project }}\n\nThis is a template for {{ name }}.\n\nDescription: {{ description }}"

      source_file =
        create_source_file(%{
          name: "README.md.liquid",
          content: template_content,
          target_path: "README.md",
          is_template: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Verify the source file has content properly loaded from FileContent
      assert source_file.content == template_content

      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:get_file_content, fn _client, _owner, _repo, "README.md" ->
        # Return different content to trigger an update
        {:ok, "# Old Content\n\nThis is old content.", %{"sha" => "old_sha"}}
      end)
      |> expect(:update_file, fn _client,
                                 _owner,
                                 _repo,
                                 _path,
                                 content,
                                 _message,
                                 _sha,
                                 _branch ->
        # Verify the content is properly rendered and base64 encoded
        expected_rendered =
          "# test-project\n\nThis is a template for target-repo.\n\nDescription: A test repository"

        expected_encoded = Base.encode64(expected_rendered)

        assert content == expected_encoded
        {200, %{"content" => %{"sha" => "new_sha"}}, %{}}
      end)

      # Perform sync
      result = Sync.sync_file(source_file, template_repo, target_repo, test_client)

      # Verify sync succeeded
      assert {:ok, "File updated successfully"} = result
    end

    test "correctly handles non-template files with FileContent schema" do
      user = create_user()

      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: "template-owner",
          full_name: "template-owner/template-repo",
          sync_mode: :direct,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: "target-owner",
          full_name: "target-owner/target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a non-template source file
      file_content = "defmodule MyApp do\n  def hello, do: :world\nend"

      source_file =
        create_source_file(%{
          name: "my_app.ex",
          content: file_content,
          target_path: "lib/my_app.ex",
          is_template: false,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Verify the source file has content properly loaded from FileContent
      assert source_file.content == file_content

      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:get_file_content, fn _client, _owner, _repo, "lib/my_app.ex" ->
        # Return different content to trigger an update
        {:ok, "defmodule MyApp do\n  def hello, do: :old\nend", %{"sha" => "old_sha"}}
      end)
      |> expect(:update_file, fn _client,
                                 _owner,
                                 _repo,
                                 _path,
                                 content,
                                 _message,
                                 _sha,
                                 _branch ->
        # Verify the content is properly encoded (should be the same as input since it's not a template)
        expected_encoded = Base.encode64(file_content)

        assert content == expected_encoded
        {200, %{"content" => %{"sha" => "new_sha"}}, %{}}
      end)

      # Perform sync
      result = Sync.sync_file(source_file, template_repo, target_repo, test_client)

      # Verify sync succeeded
      assert {:ok, "File updated successfully"} = result
    end
  end

  describe "sync_changes/5 with PR mode" do
    test "creates pull request records when creating GitHub PRs" do
      user = create_user()

      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: "template-owner",
          full_name: "template-owner/template-repo",
          sync_mode: :pr,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: "target-owner",
          full_name: "target-owner/target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source files
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          content: "config content",
          target_path: "config/config.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "readme.md",
          content: "readme content",
          target_path: "README.md",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source files with target repository
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      # Mock GitHub API calls for multi-file PR creation
      Repobot.Test.GitHubMock
      |> expect(:get_file_content, 2, fn _client, _owner, _repo, path ->
        # Return different content for each file to trigger updates
        case path do
          "config/config.ex" -> {:ok, "old config content", %{"sha" => "config_sha"}}
          "README.md" -> {:ok, "old readme content", %{"sha" => "readme_sha"}}
        end
      end)
      |> expect(:get_repo, 1, fn _client, _owner, _repo ->
        {200, %{"default_branch" => "main"}, %{}}
      end)
      |> expect(:get_ref, 2, fn _client, _owner, _repo, "heads/main" ->
        {200, %{"object" => %{"sha" => "base_sha"}}, %{}}
      end)
      |> expect(:get_commit, 2, fn _client, _owner, _repo, "base_sha" ->
        {200, %{"sha" => "base_sha", "tree" => %{"sha" => "base_tree_sha"}}, %{}}
      end)
      |> expect(:create_ref, fn _client, _owner, _repo, _ref, _sha ->
        {201, %{"ref" => "refs/heads/test-branch"}, %{}}
      end)
      |> expect(:create_tree, fn _client, _owner, _repo, _tree_entries, _base_sha ->
        {201, %{"sha" => "tree_sha"}, %{}}
      end)
      |> expect(:create_commit, fn _client, _owner, _repo, _message, _tree_sha, _parents ->
        {201, %{"sha" => "commit_sha"}, %{}}
      end)
      |> expect(:update_reference, fn _client, _owner, _repo, _ref, _sha, _force ->
        {200, %{}, %{}}
      end)
      |> expect(:create_pull_request, fn _client, _owner, _repo, _title, _body, _head, _base ->
        {201,
         %{"number" => 123, "html_url" => "https://github.com/target-owner/target-repo/pull/123"},
         %{}}
      end)

      # Verify no pull requests exist before sync
      assert Repo.all(Repobot.PullRequest) == []

      # Perform sync
      result =
        Sync.sync_changes(
          [source_file1, source_file2],
          template_repo,
          target_repo,
          test_client,
          commit_message: "Update files"
        )

      # Verify sync succeeded
      assert {:ok, "https://github.com/target-owner/target-repo/pull/123"} = result

      # Verify pull request record was created
      pull_requests = Repo.all(Repobot.PullRequest) |> Repo.preload(:source_files)
      assert length(pull_requests) == 1

      pull_request = List.first(pull_requests)
      assert pull_request.repository == "target-owner/target-repo"
      assert pull_request.pull_request_number == 123

      assert pull_request.pull_request_url ==
               "https://github.com/target-owner/target-repo/pull/123"

      assert pull_request.status == "open"

      # Verify pull request is associated with both source files
      source_file_ids = Enum.map(pull_request.source_files, & &1.id)
      assert source_file1.id in source_file_ids
      assert source_file2.id in source_file_ids
      assert length(source_file_ids) == 2
    end
  end
end
