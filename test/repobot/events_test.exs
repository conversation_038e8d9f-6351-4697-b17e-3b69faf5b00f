defmodule Repobot.EventsTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.Events

  describe "events" do
    alias Repobot.Events.Event

    test "list_events/0 returns all events" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      # Create a test event
      event =
        create_event(%{
          type: "repobot.test",
          payload: %{"test" => "data"},
          organization_id: organization.id,
          user_id: user.id,
          status: "success"
        })

      events = Events.list_events()
      assert Enum.any?(events, fn e -> e.id == event.id end)
    end

    test "get_event!/1 returns the event with given id" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      event =
        create_event(%{
          type: "repobot.test",
          payload: %{"test" => "data"},
          organization_id: organization.id,
          user_id: user.id
        })

      fetched_event = Events.get_event!(event.id)
      assert fetched_event.id == event.id
      assert fetched_event.type == "repobot.test"
      assert fetched_event.payload == %{"test" => "data"}
    end

    test "create_event/1 with valid data creates a event" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      repository =
        create_repository(%{
          organization_id: organization.id,
          user_id: user.id,
          name: "test-repo"
        })

      valid_attrs = %{
        type: "github.webhook",
        payload: %{"action" => "push"},
        organization_id: organization.id,
        user_id: user.id,
        repository_id: repository.id
      }

      assert {:ok, %Event{} = event} = Events.create_event(valid_attrs)
      assert event.type == "github.webhook"
      assert event.payload == %{"action" => "push"}
      assert event.organization_id == organization.id
      assert event.user_id == user.id
      assert event.repository_id == repository.id
    end

    test "create_event/1 with invalid data returns error changeset" do
      invalid_attrs = %{type: nil, payload: nil, organization_id: nil}
      assert {:error, %Ecto.Changeset{}} = Events.create_event(invalid_attrs)
    end

    test "log_event/5 creates an event with the given data including repository_id" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      repository =
        create_repository(%{
          organization_id: organization.id,
          user_id: user.id,
          name: "test-repo"
        })

      assert {:ok, %Event{} = event} =
               Events.log_event(
                 "github.webhook",
                 %{"event" => "push"},
                 organization.id,
                 user.id,
                 repository.id
               )

      assert event.type == "github.webhook"
      assert event.payload == %{"event" => "push"}
      assert event.organization_id == organization.id
      assert event.user_id == user.id
      assert event.repository_id == repository.id
    end

    test "log_github_event/5 creates an event with github namespace and repository_id" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      repository =
        create_repository(%{
          organization_id: organization.id,
          user_id: user.id,
          name: "test-repo"
        })

      assert {:ok, %Event{} = event} =
               Events.log_github_event(
                 "push",
                 %{"event" => "push"},
                 organization.id,
                 user.id,
                 repository.id
               )

      assert event.type == "github.push"
      assert event.payload == %{"event" => "push"}
      assert event.organization_id == organization.id
      assert event.user_id == user.id
      assert event.repository_id == repository.id
    end

    test "log_github_event/5 creates specific events for pull requests with actions" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      repository =
        create_repository(%{
          organization_id: organization.id,
          user_id: user.id,
          name: "test-repo"
        })

      assert {:ok, %Event{} = event} =
               Events.log_github_event(
                 "pull_request",
                 %{"action" => "opened"},
                 organization.id,
                 user.id,
                 repository.id
               )

      assert event.type == "github.pull_request.opened"
      assert event.payload == %{"action" => "opened"}
      assert event.organization_id == organization.id
      assert event.user_id == user.id
      assert event.repository_id == repository.id
    end

    test "log_repobot_event/5 creates an event with repobot namespace and repository_id" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      repository =
        create_repository(%{
          organization_id: organization.id,
          user_id: user.id,
          name: "test-repo"
        })

      assert {:ok, %Event{} = event} =
               Events.log_repobot_event(
                 "sync",
                 %{"template_repo" => "owner/repo"},
                 organization.id,
                 user.id,
                 repository.id
               )

      assert event.type == "repobot.sync"
      assert event.payload == %{"template_repo" => "owner/repo"}
      assert event.organization_id == organization.id
      assert event.user_id == user.id
      assert event.repository_id == repository.id
    end

    test "delete_event/1 deletes the event" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      event =
        create_event(%{
          type: "github.webhook",
          payload: %{"action" => "push"},
          organization_id: organization.id
        })

      assert {:ok, %Event{}} = Events.delete_event(event)
      assert_raise Ecto.NoResultsError, fn -> Events.get_event!(event.id) end
    end

    test "change_event/1 returns a event changeset" do
      user = create_user()
      organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

      event =
        create_event(%{
          type: "github.webhook",
          payload: %{"action" => "push"},
          organization_id: organization.id
        })

      assert %Ecto.Changeset{} = Events.change_event(event)
    end
  end

  # Helper to create an event directly
  defp create_event(attrs) do
    {:ok, event} =
      %Repobot.Events.Event{}
      |> Repobot.Events.Event.changeset(attrs)
      |> Repo.insert()

    event
  end
end
