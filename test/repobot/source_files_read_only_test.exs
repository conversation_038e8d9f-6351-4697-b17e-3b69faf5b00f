defmodule Repobot.SourceFilesReadOnlyTest do
  use Repobot.DataCase

  import Ecto.Changeset, only: [get_change: 2]

  alias Repobot.{SourceFiles, SourceFile, Repository}
  import Repobot.Test.Fixtures

  describe "read-only source files" do
    setup do
      user = create_user(%{login: "test_user", email: "<EMAIL>"})
      organization = user.default_organization

      # Create a template repository
      template_repo =
        %Repository{
          id: Ecto.UUID.generate(),
          name: "template-repo",
          owner: "test-owner",
          full_name: "test-owner/template-repo",
          template: true,
          organization_id: organization.id
        }
        |> Repo.insert!()

      # Create a read-only source file from template repository
      read_only_source_file =
        %SourceFile{
          id: Ecto.UUID.generate(),
          name: "config.yml",
          content: "# Template config",
          target_path: "config.yml",
          read_only: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: organization.id
        }
        |> Repo.insert!()

      # Create a regular (non-read-only) source file
      regular_source_file =
        %SourceFile{
          id: Ecto.UUID.generate(),
          name: "regular.txt",
          content: "Regular content",
          target_path: "regular.txt",
          read_only: false,
          user_id: user.id,
          organization_id: organization.id
        }
        |> Repo.insert!()

      %{
        user: user,
        organization: organization,
        template_repo: template_repo,
        read_only_source_file: read_only_source_file,
        regular_source_file: regular_source_file
      }
    end

    test "cannot update content of read-only source file", %{read_only_source_file: source_file} do
      result = SourceFiles.update_source_file(source_file, %{"content" => "New content"})

      assert {:error, message} = result
      assert message =~ "Cannot update read-only source file"
      assert message =~ "managed by GitHub events"
    end

    test "cannot update name of read-only source file", %{read_only_source_file: source_file} do
      result = SourceFiles.update_source_file(source_file, %{"name" => "new-name.yml"})

      assert {:error, message} = result
      assert message =~ "Cannot update read-only source file"
    end

    test "cannot update multiple fields of read-only source file", %{
      read_only_source_file: source_file
    } do
      result =
        SourceFiles.update_source_file(source_file, %{
          "content" => "New content",
          "name" => "new-name.yml"
        })

      assert {:error, message} = result
      assert message =~ "Cannot update read-only source file"
    end

    test "can update read_only flag itself", %{read_only_source_file: source_file} do
      result = SourceFiles.update_source_file(source_file, %{"read_only" => false})

      assert {:ok, updated_source_file} = result
      assert updated_source_file.read_only == false
    end

    test "can update regular (non-read-only) source file", %{regular_source_file: source_file} do
      result = SourceFiles.update_source_file(source_file, %{"content" => "Updated content"})

      assert {:ok, updated_source_file} = result
      assert updated_source_file.content == "Updated content"
    end

    test "can update read-only source file from push event", %{read_only_source_file: source_file} do
      result =
        SourceFiles.update_source_file_from_push(source_file, %{
          "content" => "New content from push"
        })

      assert {:ok, updated_source_file} = result
      assert updated_source_file.content == "New content from push"
      # Should remain read-only
      assert updated_source_file.read_only == true
    end

    test "can update multiple fields of read-only source file from push event", %{
      read_only_source_file: source_file
    } do
      result =
        SourceFiles.update_source_file_from_push(source_file, %{
          "content" => "New content from push",
          "name" => "updated_from_push.yml"
        })

      assert {:ok, updated_source_file} = result
      assert updated_source_file.content == "New content from push"
      assert updated_source_file.name == "updated_from_push.yml"
      # Should remain read-only
      assert updated_source_file.read_only == true
    end

    test "changeset validation prevents updates to read-only source file" do
      # Create a read-only source file
      source_file = %SourceFile{
        id: Ecto.UUID.generate(),
        name: "test.txt",
        content: "Original content",
        target_path: "test.txt",
        read_only: true,
        user_id: Ecto.UUID.generate(),
        organization_id: Ecto.UUID.generate()
      }

      changeset = SourceFile.changeset(source_file, %{"content" => "New content"})

      refute changeset.valid?
      assert changeset.errors[:read_only]

      assert Keyword.get(changeset.errors, :read_only) |> elem(0) =~
               "cannot modify read-only source file"
    end

    test "changeset allows updates to non-read-only source file" do
      # Create a non-read-only source file
      source_file = %SourceFile{
        id: Ecto.UUID.generate(),
        name: "test.txt",
        content: "Original content",
        target_path: "test.txt",
        read_only: false,
        user_id: Ecto.UUID.generate(),
        organization_id: Ecto.UUID.generate()
      }

      changeset = SourceFile.changeset(source_file, %{"content" => "New content"})

      assert changeset.valid?
    end

    test "changeset allows creating new source files even if read_only is true" do
      # New source file (no id)
      source_file = %SourceFile{}

      changeset =
        SourceFile.changeset(source_file, %{
          "name" => "test.txt",
          "content" => "Content",
          "read_only" => true,
          "user_id" => Ecto.UUID.generate(),
          "organization_id" => Ecto.UUID.generate()
        })

      assert changeset.valid?
    end

    test "changeset_for_push allows updates to read-only source file" do
      # Create a read-only source file
      source_file = %SourceFile{
        id: Ecto.UUID.generate(),
        name: "test.txt",
        content: "Original content",
        target_path: "test.txt",
        read_only: true,
        user_id: Ecto.UUID.generate(),
        organization_id: Ecto.UUID.generate()
      }

      changeset =
        SourceFile.changeset_for_push(source_file, %{"content" => "New content from push"})

      assert changeset.valid?
      assert get_change(changeset, :content) == "New content from push"
    end

    test "source files created with read_only flag are properly marked", %{
      user: user,
      organization: organization
    } do
      # Create a template repository
      template_repo =
        %Repository{
          id: Ecto.UUID.generate(),
          name: "template-repo",
          owner: "test-owner",
          full_name: "test-owner/template-repo",
          template: true,
          organization_id: organization.id
        }
        |> Repo.insert!()

      # Create a regular repository
      regular_repo =
        %Repository{
          id: Ecto.UUID.generate(),
          name: "regular-repo",
          owner: "test-owner",
          full_name: "test-owner/regular-repo",
          template: false,
          organization_id: organization.id
        }
        |> Repo.insert!()

      # Create source file with read_only: true (simulating template repository import)
      template_attrs = %{
        name: "config.yml",
        content: "# Template config",
        target_path: "config.yml",
        source_repository_id: template_repo.id,
        read_only: true,
        user_id: user.id,
        organization_id: organization.id
      }

      {:ok, template_source_file} = SourceFiles.create_source_file(template_attrs)
      assert template_source_file.read_only == true

      # Create source file with read_only: false (simulating regular repository import)
      regular_attrs = %{
        name: "regular.txt",
        content: "Regular content",
        target_path: "regular.txt",
        source_repository_id: regular_repo.id,
        read_only: false,
        user_id: user.id,
        organization_id: organization.id
      }

      {:ok, regular_source_file} = SourceFiles.create_source_file(regular_attrs)
      assert regular_source_file.read_only == false
    end
  end
end
