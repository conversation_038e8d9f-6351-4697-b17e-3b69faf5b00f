defmodule Repobot.RepositoriesTest do
  use ExUnit.Case, async: true
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.Repositories
  alias Repobot.Accounts.User

  # Import and setup Mox
  import Mox
  setup :verify_on_exit!

  test "list_repositories/0" do
    assert Repositories.list_repositories() == []
  end

  describe "search_repositories/2" do
    @tag load_repos: "solnic"
    test "returns repositories that match the search term" do
      user = Repo.get_by!(User, login: "solnic")
      repos = Repositories.search_repositories(user, "drops")
      assert [repo] = repos

      assert repo.full_name == "solnic/drops"
    end

    @tag load_repos: "solnic"
    test "returns empty list if no repositories match the search term" do
      user = Repo.get_by!(User, login: "solnic")
      repos = Repositories.search_repositories(user, "nonexistent")
      assert [] = repos
    end
  end

  describe "get_repository!/1" do
    test "returns the repository with given id" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      found_repo = Repositories.get_repository!(repository.id)
      assert found_repo.id == repository.id
      assert found_repo.full_name == repository.full_name
    end

    test "raises if repository does not exist" do
      assert_raise Ecto.Query.CastError, fn ->
        Repositories.get_repository!(123)
      end
    end
  end

  describe "user_repositories/2" do
    test "returns cached repositories for user when refresh is false" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository in a different organization
      other_org =
        %Repobot.Accounts.Organization{}
        |> Repobot.Accounts.Organization.changeset(%{name: "other-org"})
        |> Repo.insert!()

      other_repo =
        create_repository(%{
          name: "other-repo",
          owner: user.login,
          full_name: "#{user.login}/other-repo",
          user_id: user.id,
          organization_id: other_org.id
        })

      repos = Repositories.user_repositories(user, false)
      assert length(repos) == 1
      assert hd(repos).id == repository.id

      # Test with explicit organization_id
      repos = Repositories.user_repositories(user, false, other_org.id)
      assert length(repos) == 1
      assert hd(repos).id == other_repo.id
    end

    @tag :integration
    test "refreshes repositories from GitHub when refresh is true" do
      user = create_user()

      # Mock GitHub API response
      Mox.expect(Repobot.Test.GitHubMock, :client, 2, fn _user -> "mock_client" end)

      Mox.expect(Repobot.Test.GitHubMock, :user_repos, 1, fn _client, _login ->
        {:ok,
         [
           %{
             "name" => "test-repo",
             "owner" => %{"login" => user.login},
             "full_name" => "#{user.login}/test-repo",
             "language" => "Elixir",
             "fork" => false,
             "private" => false,
             "data" => %{}
           },
           %{
             "name" => "private-repo",
             "owner" => %{"login" => user.login},
             "full_name" => "#{user.login}/private-repo",
             "language" => "Elixir",
             "fork" => false,
             "private" => true,
             "data" => %{}
           }
         ]}
      end)

      # Create another organization
      other_org =
        %Repobot.Accounts.Organization{}
        |> Repobot.Accounts.Organization.changeset(%{name: "other-org"})
        |> Repo.insert!()

      Mox.expect(Repobot.Test.GitHubMock, :list_repos, 1, fn _client, "other-org" ->
        {:ok,
         [
           %{
             "name" => "test-repo",
             "owner" => %{"login" => "other-org"},
             "full_name" => "other-org/test-repo",
             "language" => "Elixir",
             "fork" => false,
             "private" => false,
             "data" => %{}
           }
         ]}
      end)

      # Test refresh with default organization
      repos = Repositories.user_repositories(user, true)
      assert length(repos) == 1
      assert hd(repos).name == "test-repo"
      assert hd(repos).organization_id == user.default_organization_id

      # Test refresh with other organization
      repos = Repositories.user_repositories(user, true, other_org.id)
      assert length(repos) == 1
      assert hd(repos).name == "test-repo"
      assert hd(repos).organization_id == other_org.id
    end
  end

  describe "refresh_repository_files!/2" do
    test "refreshes repository files" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Mock GitHub API to return multiple files with different types
      Mox.expect(Repobot.Test.GitHubMock, :client, fn _user -> "mock_client" end)

      tree_items = [
        %{
          "path" => "lib/test.ex",
          "type" => "file",
          "size" => 100,
          "sha" => "abc123"
        },
        %{
          "path" => "test/test_helper.ex",
          "type" => "file",
          "size" => 50,
          "sha" => "def456"
        },
        %{
          "path" => "mix.exs",
          "type" => "file",
          "size" => 200,
          "sha" => "ghi789"
        }
      ]

      Mox.expect(Repobot.Test.GitHubMock, :get_tree, fn _client, _owner, _repo ->
        {:ok, tree_items}
      end)

      assert {:ok, updated_repository} =
               Repositories.refresh_repository_files!(repository.id, user)

      # Verify all files were created
      repository_files = updated_repository.files
      assert length(repository_files) == 3

      # Verify each file has correct attributes
      Enum.each(tree_items, fn item ->
        file = Enum.find(repository_files, &(&1.path == item["path"]))
        assert file != nil
        assert file.type == item["type"]
        assert file.size == item["size"]
        assert file.sha == item["sha"]
        assert file.name == Path.basename(item["path"])
        assert file.repository_id == repository.id
      end)

      # Test updating existing files when SHA changes
      new_tree_items = [
        %{
          "path" => "lib/test.ex",
          "type" => "file",
          # Changed size
          "size" => 120,
          # Changed SHA
          "sha" => "abc123_new"
        },
        %{
          "path" => "test/test_helper.ex",
          "type" => "file",
          "size" => 50,
          # Same SHA
          "sha" => "def456"
        },
        %{
          "path" => "mix.exs",
          "type" => "file",
          "size" => 200,
          # Same SHA
          "sha" => "ghi789"
        }
      ]

      Mox.expect(Repobot.Test.GitHubMock, :client, fn _user -> "mock_client" end)

      Mox.expect(Repobot.Test.GitHubMock, :get_tree, fn _client, _owner, _repo ->
        {:ok, new_tree_items}
      end)

      assert {:ok, updated_repository} =
               Repositories.refresh_repository_files!(repository.id, user)

      updated_files = updated_repository.files

      # Verify file updates
      changed_file = Enum.find(updated_files, &(&1.path == "lib/test.ex"))
      assert changed_file.sha == "abc123_new"
      assert changed_file.size == 120
      # Content should be cleared for changed files
      assert changed_file.content_updated_at == nil

      # Verify unchanged files retain their state
      unchanged_file = Enum.find(updated_files, &(&1.path == "test/test_helper.ex"))
      assert unchanged_file.sha == "def456"
      assert unchanged_file.size == 50
    end

    test "handles GitHub API errors gracefully" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      Mox.expect(Repobot.Test.GitHubMock, :client, fn _user -> "mock_client" end)

      Mox.expect(Repobot.Test.GitHubMock, :get_tree, fn _client, _owner, _repo ->
        {:error, "API rate limit exceeded"}
      end)

      assert {:error, "API rate limit exceeded"} =
               Repositories.refresh_repository_files!(repository.id, user)
    end
  end

  describe "source file associations" do
    test "add_source_file/2 associates a source file with a repository" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file = create_source_file(%{name: "test.ex", content: "test"})

      assert {:ok, _} = Repositories.add_source_file(repository, source_file)

      repository = Repositories.get_repository!(repository.id)
      assert length(repository.source_files) == 1
      assert hd(repository.source_files).id == source_file.id
    end

    test "remove_source_file/2 removes association between repository and source file" do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file = create_source_file(%{name: "test.ex", content: "test"})

      # First add the association
      {:ok, _} = Repositories.add_source_file(repository, source_file)

      # Then remove it
      {1, nil} = Repositories.remove_source_file(repository, source_file)

      repository = Repositories.get_repository!(repository.id)
      assert repository.source_files == []
    end
  end

  describe "get_repository_by/1" do
    test "returns the repository matching the options" do
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      %{id: id1} = Repositories.get_repository_by(full_name: repo.full_name)
      assert id1 == repo.id

      %{id: id2} = Repositories.get_repository_by(name: "test-repo")
      assert id2 == repo.id
    end

    test "returns nil if no repository matches the options" do
      assert Repositories.get_repository_by(full_name: "non/existent") == nil
    end
  end

  describe "template repository folder associations" do
    test "automatically propagates source files to target repositories when template repository is added to folder" do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create target repositories in the folder
      target_repo1 =
        create_repository(%{
          name: "target-repo-1",
          owner: user.login,
          full_name: "#{user.login}/target-repo-1",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      target_repo2 =
        create_repository(%{
          name: "target-repo-2",
          owner: user.login,
          full_name: "#{user.login}/target-repo-2",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository with source files
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id
        })

      # Create source files for the template repository
      source_file1 =
        create_source_file(%{
          name: "README.md",
          content: "# Template README",
          target_path: "README.md",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "config.yml",
          content: "config: template",
          target_path: "config.yml",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Verify target repositories have no source files initially
      target_repo1_loaded = Repositories.get_repository!(target_repo1.id)
      target_repo2_loaded = Repositories.get_repository!(target_repo2.id)
      assert target_repo1_loaded.source_files == []
      assert target_repo2_loaded.source_files == []

      # Add template repository to the folder
      {:ok, _} = Repositories.add_template_folder(template_repo, folder)

      # Verify that source files are automatically propagated to target repositories
      target_repo1_updated = Repositories.get_repository!(target_repo1.id)
      target_repo2_updated = Repositories.get_repository!(target_repo2.id)

      # Both target repositories should now have the template's source files
      target_repo1_source_file_ids = Enum.map(target_repo1_updated.source_files, & &1.id)
      target_repo2_source_file_ids = Enum.map(target_repo2_updated.source_files, & &1.id)

      assert source_file1.id in target_repo1_source_file_ids
      assert source_file2.id in target_repo1_source_file_ids
      assert source_file1.id in target_repo2_source_file_ids
      assert source_file2.id in target_repo2_source_file_ids
    end

    test "does not duplicate source file associations when template repository is already associated with folder" do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create a target repository in the folder
      target_repo =
        create_repository(%{
          name: "target-repo",
          owner: user.login,
          full_name: "#{user.login}/target-repo",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository with source files
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id
        })

      # Create a source file for the template repository
      source_file =
        create_source_file(%{
          name: "README.md",
          content: "# Template README",
          target_path: "README.md",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Add template repository to the folder (first time)
      {:ok, _} = Repositories.add_template_folder(template_repo, folder)

      # Verify source file is associated with target repository
      target_repo_updated = Repositories.get_repository!(target_repo.id)
      assert length(target_repo_updated.source_files) == 1
      assert hd(target_repo_updated.source_files).id == source_file.id

      # Add template repository to the folder again (should not duplicate)
      {:ok, _} = Repositories.add_template_folder(template_repo, folder)

      # Verify source file is still associated only once
      target_repo_final = Repositories.get_repository!(target_repo.id)
      assert length(target_repo_final.source_files) == 1
      assert hd(target_repo_final.source_files).id == source_file.id
    end

    test "automatically unsyncs source files from target repositories when template repository is removed from folder" do
      user = create_user()

      # Create a folder
      folder =
        create_folder(%{
          name: "Test Folder",
          organization_id: user.default_organization_id
        })

      # Create target repositories in the folder
      target_repo1 =
        create_repository(%{
          name: "target-repo-1",
          owner: user.login,
          full_name: "#{user.login}/target-repo-1",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      target_repo2 =
        create_repository(%{
          name: "target-repo-2",
          owner: user.login,
          full_name: "#{user.login}/target-repo-2",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository with source files
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id
        })

      source_file1 =
        create_source_file(%{
          name: "test1.ex",
          content: "test content 1",
          target_path: "test1.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "test2.ex",
          content: "test content 2",
          target_path: "test2.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Add template repository to the folder (this should propagate source files)
      {:ok, _} = Repositories.add_template_folder(template_repo, folder)

      # Verify that source files are propagated to target repositories
      target_repo1_with_files = Repositories.get_repository!(target_repo1.id)
      target_repo2_with_files = Repositories.get_repository!(target_repo2.id)

      assert length(target_repo1_with_files.source_files) == 2
      assert length(target_repo2_with_files.source_files) == 2

      source_file_ids = [source_file1.id, source_file2.id]
      target_repo1_file_ids = Enum.map(target_repo1_with_files.source_files, & &1.id)
      target_repo2_file_ids = Enum.map(target_repo2_with_files.source_files, & &1.id)

      assert Enum.all?(source_file_ids, &(&1 in target_repo1_file_ids))
      assert Enum.all?(source_file_ids, &(&1 in target_repo2_file_ids))

      # Remove template repository from the folder (this should unsync source files)
      {:ok, _} = Repositories.remove_template_folder(template_repo, folder)

      # Verify that source files are unsynced from target repositories
      target_repo1_after_removal = Repositories.get_repository!(target_repo1.id)
      target_repo2_after_removal = Repositories.get_repository!(target_repo2.id)

      assert target_repo1_after_removal.source_files == []
      assert target_repo2_after_removal.source_files == []
    end
  end

  describe "get_template_repository_by/1" do
    test "returns the template repository matching the options" do
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      _normal_repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, %{id: id1}} =
        Repositories.get_template_repository_by(full_name: template_repo.full_name)

      assert id1 == template_repo.id

      {:ok, %{id: id2}} = Repositories.get_template_repository_by(name: "template-repo")
      assert id2 == template_repo.id
    end

    test "returns {:error, :not_found} if no template repository matches the options" do
      user = create_user()

      normal_repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      assert Repositories.get_template_repository_by(full_name: "non/existent") ==
               {:error, :not_found}

      assert Repositories.get_template_repository_by(full_name: normal_repo.full_name) ==
               {:error, :not_found}
    end
  end
end
