defmodule Repobot.SourceFilesImportTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  import Mox

  alias <PERSON>obot.SourceFiles

  setup :verify_on_exit!

  setup do
    # Mock AI to avoid tag inference calls
    Repobot.Test.AIMock
    |> stub(:infer_tags, fn _source_file, _organization -> {:error, "tags disabled for test"} end)

    :ok
  end

  describe "import_file/2 with repository_file association" do
    test "establishes repository_file association when importing from a source repository" do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository file first
      repository_file =
        create_repository_file(%{
          repository_id: template_repo.id,
          path: "config.ex.liquid",
          name: "config.ex.liquid",
          type: "file",
          size: 100,
          sha: "abc123",
          content: "# Config for {{ repo.name }}"
        })

      # Import a source file from the template repository
      attrs = %{
        name: "config.ex.liquid",
        target_path: "config.ex",
        content: "# Config for {{ repo.name }}",
        is_template: true,
        source_repository_id: template_repo.id,
        user_id: user.id,
        organization_id: user.default_organization_id
      }

      {:ok, source_file} = SourceFiles.import_file(attrs, [target_repo])

      # Verify the source file is associated with the repository file
      assert source_file.repository_file_id == repository_file.id
      assert source_file.source_repository_id == template_repo.id
    end

    test "does not establish association when no matching repository file exists" do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Import a source file without a corresponding repository file
      attrs = %{
        name: "nonexistent.ex.liquid",
        target_path: "nonexistent.ex",
        content: "# This file doesn't exist in the repository",
        is_template: true,
        source_repository_id: template_repo.id,
        user_id: user.id,
        organization_id: user.default_organization_id
      }

      {:ok, source_file} = SourceFiles.import_file(attrs, [target_repo])

      # Verify no association is established
      assert is_nil(source_file.repository_file_id)
      assert source_file.source_repository_id == template_repo.id
    end

    test "does not establish association when no source_repository_id is provided" do
      user = create_user()

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Import a source file without a source repository
      attrs = %{
        name: "manual.ex",
        target_path: "manual.ex",
        content: "# Manually created file",
        is_template: false,
        user_id: user.id,
        organization_id: user.default_organization_id
      }

      {:ok, source_file} = SourceFiles.import_file(attrs, [target_repo])

      # Verify no association is established
      assert is_nil(source_file.repository_file_id)
      assert is_nil(source_file.source_repository_id)
    end

    test "gets content from repository file when no content provided and source_repository_id is set" do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository file first
      repository_file =
        create_repository_file(%{
          repository_id: template_repo.id,
          path: "config.ex.liquid",
          name: "config.ex.liquid",
          type: "file",
          size: 100,
          sha: "abc123",
          content: "# Config for {{ repo.name }}"
        })

      # Import a source file from the template repository without providing content
      attrs = %{
        name: "config.ex.liquid",
        target_path: "config.ex",
        is_template: true,
        source_repository_id: template_repo.id,
        user_id: user.id,
        organization_id: user.default_organization_id
        # Note: no content provided
      }

      {:ok, source_file} = SourceFiles.import_file(attrs, [target_repo])

      # Verify the source file is associated with the repository file
      assert source_file.repository_file_id == repository_file.id
      assert source_file.source_repository_id == template_repo.id
      # Content should come from the repository file
      assert source_file.content == "# Config for {{ repo.name }}"
    end

    test "uses provided content even when repository file exists" do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a target repository
      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository file first
      repository_file =
        create_repository_file(%{
          repository_id: template_repo.id,
          path: "config.ex.liquid",
          name: "config.ex.liquid",
          type: "file",
          size: 100,
          sha: "abc123",
          content: "# Repository file content"
        })

      # Import a source file from the template repository with explicit content
      attrs = %{
        name: "config.ex.liquid",
        target_path: "config.ex",
        content: "# Explicit content provided",
        is_template: true,
        source_repository_id: template_repo.id,
        user_id: user.id,
        organization_id: user.default_organization_id
      }

      {:ok, source_file} = SourceFiles.import_file(attrs, [target_repo])

      # Verify the source file is associated with the repository file
      assert source_file.repository_file_id == repository_file.id
      assert source_file.source_repository_id == template_repo.id
      # Content should be the explicitly provided content, not from repository file
      assert source_file.content == "# Explicit content provided"
    end
  end

  describe "import_file_content/3 with repository_file association" do
    test "establishes repository_file association when importing content from a repository" do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: "testowner",
          full_name: "testowner/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository file with content
      repository_file =
        create_repository_file(%{
          repository_id: template_repo.id,
          path: "config.ex",
          name: "config.ex",
          type: "file",
          size: 100,
          sha: "abc123",
          content: "# Repository file content"
        })

      # Create a source file without association
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config.ex",
          content: "# Old content",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Import content from the repository (no GitHub API calls needed)
      {:ok, updated_source_file} =
        SourceFiles.import_file_content(source_file, "testowner/template-repo", user)

      # Verify the source file is now associated with the repository file
      assert updated_source_file.repository_file_id == repository_file.id
      # Content should come from the repository file, not GitHub
      assert updated_source_file.content == "# Repository file content"
    end

    test "returns error when repository file does not exist" do
      user = create_user()

      # Create a template repository
      _template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          owner: "testowner",
          full_name: "testowner/template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a source file without association
      source_file =
        create_source_file(%{
          name: "nonexistent.ex",
          target_path: "nonexistent.ex",
          content: "# Old content",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Import content from the repository
      {:error, reason} =
        SourceFiles.import_file_content(source_file, "testowner/template-repo", user)

      assert reason == "Repository file not found: nonexistent.ex"
    end

    test "returns error when repository does not exist" do
      user = create_user()

      # Create a source file without association
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config.ex",
          content: "# Old content",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Import content from a non-existent repository
      {:error, reason} =
        SourceFiles.import_file_content(source_file, "nonexistent/repo", user)

      assert reason == "Repository not found: nonexistent/repo"
    end
  end
end
