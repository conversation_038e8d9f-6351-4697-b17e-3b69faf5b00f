const AutoDismissFlash = {
  mounted() {
    this.startCountdown()
  },

  startCountdown() {
    // Get the countdown duration (default 5 seconds)
    const duration = parseInt(this.el.dataset.duration || '5')
    let timeLeft = duration

    // Find the countdown and dismiss button elements
    const countdownElement = this.el.querySelector('.countdown-circle')
    const dismissButton = this.el.querySelector('.dismiss-button')

    if (!countdownElement || !dismissButton) return

    // Set initial countdown value
    this.updateCountdown(countdownElement, timeLeft, duration)

    // Start the countdown timer
    this.timer = setInterval(() => {
      timeLeft--
      this.updateCountdown(countdownElement, timeLeft, duration)

      if (timeLeft <= 0) {
        this.dismissFlash()
      }
    }, 1000)

    // Show dismiss button and hide countdown on hover
    this.el.addEventListener('mouseenter', () => {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      countdownElement.style.display = 'none'
      dismissButton.style.display = 'flex'
    })

    // Hide dismiss button and show countdown when mouse leaves
    this.el.addEventListener('mouseleave', () => {
      dismissButton.style.display = 'none'
      countdownElement.style.display = 'flex'

      if (!this.timer && timeLeft > 0) {
        this.timer = setInterval(() => {
          timeLeft--
          this.updateCountdown(countdownElement, timeLeft, duration)

          if (timeLeft <= 0) {
            this.dismissFlash()
          }
        }, 1000)
      }
    })
  },

  updateCountdown(element, timeLeft, duration) {
    // Update the countdown display with circular progress
    const numberElement = element.querySelector('.countdown-number')
    const circleElement = element.querySelector('.countdown-progress')

    if (numberElement) {
      numberElement.textContent = timeLeft
    }

    if (circleElement) {
      const circumference = 2 * Math.PI * 10 // radius is 10
      const progress = (duration - timeLeft) / duration
      const strokeDashoffset = circumference * (1 - progress)
      circleElement.style.strokeDashoffset = strokeDashoffset
    }
  },

  dismissFlash() {
    // Clear the timer
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // Trigger the existing flash dismiss functionality
    const dismissButton = this.el.querySelector('button[aria-label="close"]')
    if (dismissButton) {
      dismissButton.click()
    }
  },

  destroyed() {
    // Clean up timer when component is destroyed
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  }
}

export default AutoDismissFlash
