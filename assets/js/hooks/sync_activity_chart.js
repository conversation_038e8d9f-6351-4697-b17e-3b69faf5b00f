import Chart from 'chart.js/auto';

const SyncActivityChart = {
  mounted() {
    this.chart = null;
    this.initChart();

    this.handleEvent("update_sync_activity", ({ data }) => {
      this.updateChart(data);
    });
  },

  updated() {
    // Re-initialize chart if data attributes change
    const newData = JSON.parse(this.el.dataset.chartData || '[]');
    if (this.chart && newData.length > 0) {
      this.updateChart(newData);
    }
  },

  initChart() {
    const canvas = this.el.querySelector('canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const chartData = JSON.parse(this.el.dataset.chartData || '[]');

    this.chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: chartData.map(d => this.formatDateLabel(d.date)),
        datasets: [{
          label: 'Sync Events',
          data: chartData.map(d => d.count),
          borderColor: 'rgb(59, 130, 246)', // blue-500
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointRadius: 0,
          pointHoverRadius: 4,
          pointHoverBackgroundColor: 'rgb(59, 130, 246)',
          pointHoverBorderColor: 'white',
          pointHoverBorderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 6,
            displayColors: false,
            callbacks: {
              title: function (context) {
                return context[0].label;
              },
              label: function (context) {
                const count = context.parsed.y;
                return `${count} sync event${count !== 1 ? 's' : ''}`;
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            grid: {
              display: false
            },
            ticks: {
              font: {
                size: 10
              },
              color: 'rgb(148, 163, 184)' // slate-400
            }
          },
          y: {
            display: false,
            grid: {
              display: false
            },
            beginAtZero: true
          }
        },
        elements: {
          point: {
            radius: 0
          }
        }
      }
    });
  },

  updateChart(data) {
    if (!this.chart) return;

    this.chart.data.labels = data.map(d => this.formatDateLabel(d.date));
    this.chart.data.datasets[0].data = data.map(d => d.count);
    this.chart.update('none'); // No animation for real-time updates
  },

  formatDateLabel(dateString) {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      // Return day of week for other days
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    }
  },

  destroyed() {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
  }
};

export default SyncActivityChart;
