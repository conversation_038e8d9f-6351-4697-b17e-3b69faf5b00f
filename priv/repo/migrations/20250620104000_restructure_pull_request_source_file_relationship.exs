defmodule Repobot.Repo.Migrations.RestructurePullRequestSourceFileRelationship do
  use Ecto.Migration

  def up do
    # Create the many-to-many join table
    create table(:pull_request_source_files, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :pull_request_id, references(:pull_requests, type: :binary_id, on_delete: :delete_all),
        null: false

      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:pull_request_source_files, [:pull_request_id, :source_file_id])
    create index(:pull_request_source_files, [:source_file_id])

    # Migrate existing data from pull_requests.source_file_id to the join table
    execute """
    INSERT INTO pull_request_source_files (id, pull_request_id, source_file_id, inserted_at, updated_at)
    SELECT gen_random_uuid(), id, source_file_id, inserted_at, updated_at
    FROM pull_requests
    WHERE source_file_id IS NOT NULL
    """

    # Remove the old foreign key constraint and column
    alter table(:pull_requests) do
      remove :source_file_id
    end
  end

  def down do
    # Add back the source_file_id column
    alter table(:pull_requests) do
      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: true
    end

    # Migrate data back (only for pull requests with exactly one source file)
    execute """
    UPDATE pull_requests
    SET source_file_id = prsf.source_file_id
    FROM pull_request_source_files prsf
    WHERE pull_requests.id = prsf.pull_request_id
    AND pull_requests.id IN (
      SELECT pull_request_id
      FROM pull_request_source_files
      GROUP BY pull_request_id
      HAVING COUNT(*) = 1
    )
    """

    # Drop the join table
    drop table(:pull_request_source_files)
  end
end
