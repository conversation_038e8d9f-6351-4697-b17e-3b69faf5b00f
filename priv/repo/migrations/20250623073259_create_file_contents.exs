defmodule Repobot.Repo.Migrations.CreateFileContents do
  use Ecto.Migration

  def change do
    # Create the file_contents table
    create table(:file_contents, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :blob, :text, null: false

      # Optional associations - at least one must be present
      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all)

      add :repository_file_id,
          references(:repository_files, type: :binary_id, on_delete: :delete_all)

      timestamps(type: :utc_datetime)
    end

    # Add indexes for foreign keys
    create index(:file_contents, [:source_file_id])
    create index(:file_contents, [:repository_file_id])

    # Add constraint to ensure at least one file association exists
    create constraint(:file_contents, :must_belong_to_at_least_one_file,
             check: "source_file_id IS NOT NULL OR repository_file_id IS NOT NULL"
           )
  end
end
