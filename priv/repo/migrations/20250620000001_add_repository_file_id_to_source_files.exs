defmodule Repobot.Repo.Migrations.AddRepositoryFileIdToSourceFiles do
  use Ecto.Migration

  def change do
    alter table(:source_files) do
      add :repository_file_id,
          references(:repository_files, type: :binary_id, on_delete: :nilify_all)
    end

    create index(:source_files, [:repository_file_id])

    # Update existing source files to set their repository_file_id
    execute(
      """
      UPDATE source_files
      SET repository_file_id = rf.id
      FROM repository_files rf
      WHERE source_files.source_repository_id = rf.repository_id
        AND source_files.repository_file_id IS NULL
        AND source_files.target_path = rf.path
      """,
      # Rollback SQL
      "UPDATE source_files SET repository_file_id = NULL"
    )
  end
end
