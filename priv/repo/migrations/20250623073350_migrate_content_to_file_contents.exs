defmodule Repobot.Repo.Migrations.MigrateContentToFileContents do
  use Ecto.Migration
  import Ecto.Query

  def up do
    # Step 1: Create shared FileContent records for source files that are associated with repository files
    # Repository file content is already base64 encoded, source file content needs encoding
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT
      gen_random_uuid(),
      COALESCE(rf.content, encode(sf.content::bytea, 'base64')),  -- Use repository file content (already encoded) or encode source file content
      sf.id,
      sf.repository_file_id,
      NOW(),
      NOW()
    FROM source_files sf
    INNER JOIN repository_files rf ON sf.repository_file_id = rf.id
    WHERE sf.repository_file_id IS NOT NULL
      AND (sf.content IS NOT NULL OR rf.content IS NOT NULL)
    """

    # Step 2: Create exclusive FileContent records for source files without repository file associations
    # Source file content needs to be base64 encoded
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT
      gen_random_uuid(),
      encode(sf.content::bytea, 'base64'),  -- Encode source file content as base64
      sf.id,
      NULL,
      NOW(),
      NOW()
    FROM source_files sf
    WHERE sf.repository_file_id IS NULL
      AND sf.content IS NOT NULL
    """

    # Step 3: Create exclusive FileContent records for repository files without associated source files
    # Repository file content is already base64 encoded
    execute """
    INSERT INTO file_contents (id, blob, source_file_id, repository_file_id, inserted_at, updated_at)
    SELECT
      gen_random_uuid(),
      rf.content,  -- Repository file content is already base64 encoded
      NULL,
      rf.id,
      NOW(),
      NOW()
    FROM repository_files rf
    LEFT JOIN source_files sf ON sf.repository_file_id = rf.id
    WHERE sf.id IS NULL
      AND rf.content IS NOT NULL
    """
  end

  def down do
    # Step 1: Copy content back from FileContent to source_files (decode base64 to plain text)
    execute """
    UPDATE source_files
    SET content = convert_from(decode(fc.blob, 'base64'), 'UTF8')
    FROM file_contents fc
    WHERE fc.source_file_id = source_files.id
    """

    # Step 2: Copy content back from FileContent to repository_files (keep as base64 encoded)
    # Only for exclusive content (where source_file_id IS NULL)
    execute """
    UPDATE repository_files
    SET content = fc.blob
    FROM file_contents fc
    WHERE fc.repository_file_id = repository_files.id
      AND fc.source_file_id IS NULL
    """

    # Step 3: Delete all FileContent records
    execute "DELETE FROM file_contents"
  end
end
